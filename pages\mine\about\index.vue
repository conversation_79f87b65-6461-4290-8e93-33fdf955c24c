<template>
    <view class="about-container">
        <view class="header-section text-center">
            <image style="width: 150rpx;height: 150rpx;" src="/static/logo200.png" mode="widthFix">
            </image>
            <uni-title type="h2" title="卓越移动端"></uni-title>
        </view>

        <view class="copyright">
            <view>Copyright &copy; 2022 ruoyi.vip All Rights Reserved.</view>
        </view>
    </view>
</template>

<script>
    export default {
        data() {
            return {
                url: getApp().globalData.config.appInfo.site_url,
                version: getApp().globalData.config.appInfo.version
            }
        }
    }
</script>

<style lang="scss">
    page {
        background-color: #f8f8f8;
    }

    .copyright {
        margin-top: 50 rpx;
        text-align: center;
        line-height: 60 rpx;
        color: #999;
    }

    .header-section {
        display: flex;
        padding: 30 rpx 0 0;
        flex-direction: column;
        align-items: center;
    }
</style>
