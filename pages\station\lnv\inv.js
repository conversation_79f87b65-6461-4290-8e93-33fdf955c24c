/**
 * 逆变器页面工具函数
 */

// 防抖函数 - 优化版本，立即执行第一次调用
export const debounce = (fn, delay = 300, immediate = false) => {
  let timer = null;
  let lastArgs = null;
  
  return function(...args) {
    lastArgs = args;
    
    // 是否立即执行
    const callNow = immediate && !timer;
    
    // 清除现有定时器
    if (timer) clearTimeout(timer);
    
    // 设置新的定时器
    timer = setTimeout(() => {
      timer = null;
      
      // 如果不是立即执行，则在延迟后执行
      if (!immediate) fn.apply(this, lastArgs);
    }, delay);
    
    // 如果是立即执行，则立即调用函数
    if (callNow) fn.apply(this, args);
  };
};

// 节流函数 - 用于控制高频事件触发频率
export const throttle = (fn, delay = 300) => {
  let last = 0;
  let timer = null;
  
  return function(...args) {
    const now = Date.now();
    
    // 距离上次执行的时间
    const diff = now - last;
    
    // 清除之前的定时器
    if (timer) clearTimeout(timer);
    
    if (diff >= delay) {
      // 如果距离上次执行的时间超过了delay，则立即执行
      last = now;
      fn.apply(this, args);
    } else {
      // 否则，设置一个定时器，在剩余时间后执行
      timer = setTimeout(() => {
        last = Date.now();
        fn.apply(this, args);
      }, delay - diff);
    }
  };
};

// 预加载缓存，用于存储不同类型的图表数据
let chartDataCache = {
  powerData: null,
  dcData: {
    current: null,
    voltage: null
  },
  acData: {
    current: null,
    voltage: null
  }
};

// 清除缓存数据
export const clearChartCache = () => {
  chartDataCache = {
    powerData: null,
    dcData: {
      current: null,
      voltage: null
    },
    acData: {
      current: null,
      voltage: null
    }
  };
};

// 从缓存获取图表数据
export const getChartDataFromCache = (tabIndex, dataType) => {
  switch (tabIndex) {
    case 0:
      return chartDataCache.powerData;
    case 1:
      return dataType === 'current' ? chartDataCache.dcData.current : chartDataCache.dcData.voltage;
    case 2:
      return dataType === 'current' ? chartDataCache.acData.current : chartDataCache.acData.voltage;
    default:
      return null;
  }
};

// 设置缓存图表数据
export const setChartDataCache = (tabIndex, dataType, data) => {
  switch (tabIndex) {
    case 0:
      chartDataCache.powerData = data;
      break;
    case 1:
      if (dataType === 'current') {
        chartDataCache.dcData.current = data;
      } else {
        chartDataCache.dcData.voltage = data;
      }
      break;
    case 2:
      if (dataType === 'current') {
        chartDataCache.acData.current = data;
      } else {
        chartDataCache.acData.voltage = data;
      }
      break;
  }
};

// 日期格式化函数
export const formatDate = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

export const formatYearMonth = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  return `${year}-${month}`;
};

// 时间数组格式化函数
export const formatTimeArray = (timeArray, viewType) => {
  // 如果是总视图，直接返回原始数组
  if (viewType === 3) {
    return timeArray;
  }

  return timeArray.map(time => {
    // 处理四位数的时间格式，如 "1430" => "14:30"
    if (time.length === 4) {
      return `${time.slice(0, 2)}:${time.slice(2)}`;
    }
    // 处理三位数的时间格式，如 "430" => "4:30"
    if (time.length === 3) {
      return `${time.slice(0, 1)}:${time.slice(1)}`;
    }
    return time;
  });
};

// 图表处理函数
export const getChartTitle = (tabIndex) => {
  switch (tabIndex) {
    case 0:
      return "功率曲线";
    case 1:
      return "直流曲线";
    case 2:
      return "交流曲线";
    default:
      return "";
  }
};

export const getLeftAxisLabel = (tabIndex, dataType) => {
  switch (tabIndex) {
    case 0:
      return "功率(kW)";
    case 1:
      return dataType === 'current' ? "电流(A)" : "电压(V)";
    case 2:
      return dataType === 'current' ? "电流(A)" : "电压(V)";
    default:
      return "";
  }
};

export const getRightAxisLabel = () => {
  return "电压(V)";
};

export const getLegendItems = (tabIndex, currentDataType, visibleLegendItems) => {
  switch (tabIndex) {
    case 0:
      return ["总直流功率", "总有功功率"];
    case 1:
      if (currentDataType === 'current') {
        return visibleLegendItems;
      } else {
        return visibleLegendItems.map(item => `${item}电压`);
      }
    case 2:
      if (currentDataType === 'current') {
        return ["A相电流", "B相电流", "C相电流"];
      } else {
        return ["AB相电压", "BC相电压", "CA相电压"];
      }
    default:
      return [];
  }
};

export const getLegendTitle = (tabIndex, dataType) => {
  if (tabIndex === 1) {
    return dataType === 'current' ? 'PV电流 (A)' : 'PV电压 (V)';
  } else if (tabIndex === 2) {
    return dataType === 'current' ? '相电流 (A)' : '相电压 (V)';
  }
  return '';
};

// 生成获取日期参数函数
export const getDateParams = (date, viewType) => {
  const params = {
    equipmentId: null
  };
  
  switch (viewType) {
    case 0: // 日
      params.year = date.getFullYear();
      params.month = date.getMonth() + 1;
      params.day = date.getDate();
      break;
    case 1: // 月
      params.year = date.getFullYear();
      params.month = date.getMonth() + 1;
      break;
    case 2: // 年
      params.year = date.getFullYear();
      break;
    // case 3：总计视图不需要传递额外参数
  }
  
  return params;
};

// 获取图表数据类型参数
export const getChartDataTypeParams = (tabIndex) => {
  switch (tabIndex) {
    case 0: // 功率
      return [11, 12]; // 总直流功率,总有功功率
    case 1: // 直流
      return [1, 2]; // PV电流,PV电压
    case 2: // 交流
      return [5, 6, 7, 8, 9, 10]; // A/B/C相电流和电压
    default:
      return [11]; // 默认显示总直流功率
  }
};

// 处理PV数据
export const processPVData = (responseData) => {
  if (!responseData) return [];
  
  const pvData = [];
  
  // 遍历1-30个PV
  for (let i = 1; i <= 30; i++) {
    const currentKey = `pv${i.toString().padStart(2, '0')}Current`;  // pv01Current
    const powerKey = `pv${i}Power`;      // pv1Power
    const voltageKey = `pv${i}Voltage`;  // pv1Voltage

    if (responseData[currentKey] !== undefined ||
        responseData[voltageKey] !== undefined ||
        responseData[powerKey] !== undefined) {
      pvData.push({
        id: `PV${i}`,
        Current: responseData[currentKey] || '0.00',
        Voltage: responseData[voltageKey] || '0.00',
        Power: responseData[powerKey] || '0.00'
      });
    }
  }
  
  return pvData;
};

// 处理MPPT数据
export const processMPPTData = (responseData) => {
  if (!responseData) return [];
  
  const mpptData = [];
  
  // 遍历MPPT数据
  for (let i = 1; i <= 10; i++) {
    const currentKey = `mppt${i.toString().padStart(2, '0')}Current`;  // mppt01Current
    const voltageKey = `mppt${i.toString().padStart(2, '0')}Voltage`;  // mppt01Voltage
    const powerKey = `mppt${i.toString().padStart(2, '0')}Power`;      // mppt01Power

    // 检查至少有一个值不为 null
    if (responseData[currentKey] !== null ||
        responseData[voltageKey] !== null ||
        responseData[powerKey] !== null) {
      mpptData.push({
        id: `MPPT${i}`,
        Current: responseData[currentKey] || '0.00',
        Voltage: responseData[voltageKey] || '0.00',
        Power: responseData[powerKey] || '0.00'
      });
    }
  }
  
  return mpptData;
};

// 处理交流信息数据
export const processACData = (responseData) => {
  if (!responseData) return [];
  
  return [
    {
      id: 'A相',
      Current: responseData.aphaseIa || '0.00',
      Voltage: responseData.aphaseVa || '0.00',
    },
    {
      id: 'B相',
      Current: responseData.bphaseIb || '0.00',
      Voltage: responseData.bphaseVb || '0.00',
    },
    {
      id: 'C相',
      Current: responseData.cphaseIc || '0.00',
      Voltage: responseData.cphaseVc || '0.00',
    }
  ];
};

// 处理图表数据响应
export const processChartData = (data, tabIndex, currentDataType, formattedTimes) => {
  if (!data || !data.xaxis || !data.xaxis.length) {
    console.warn('No data or xaxis received');
    return {
      powerData: createEmptyPowerData(),
      dcData: createEmptyDCData(),
      acData: createEmptyACData()
    };
  }
  
  let result = {
    powerData: null,
    dcData: null,
    acData: null
  };
  
  switch (tabIndex) {
    case 0: // 功率
      result.powerData = processPowerChartData(data, formattedTimes);
      break;
    case 1: // 直流
      result.dcData = processDCChartData(data, formattedTimes);
      break;
    case 2: // 交流
      result.acData = processACChartData(data, formattedTimes);
      break;
  }
  
  return result;
};

// 处理功率图表数据
const processPowerChartData = (data, formattedTimes) => {
  // 处理直流功率和有功功率数据
  const dcPowerValues = data.dcPower?.map(power =>
    Math.round((isNaN(power) ? 0 : power) / 100) / 10
  ) || [];

  const acPowerValues = data.acActivePower?.map(power =>
    Math.round((isNaN(power) ? 0 : power) / 100) / 10
  ) || [];

  return {
    categories: formattedTimes,
    series: [
      {
        name: "总直流功率",
        data: dcPowerValues
      },
      {
        name: "总有功功率",
        data: acPowerValues
      }
    ]
  };
};

// 处理直流图表数据
const processDCChartData = (data, formattedTimes) => {
  // 收集所有PV数据
  const pvCurrentSeries = [];
  const pvVoltageSeries = [];

  // 动态检测所有可用的PV数据
  for (let i = 1; i <= 18; i++) {
    const pvNum = i.toString().padStart(2, '0');
    const currentKey = `pv${pvNum}Current`;
    const voltageKey = `pv${pvNum}Voltage`;

    if (data[currentKey] && data[currentKey].some(val => val !== null && val !== undefined)) {
      pvCurrentSeries.push({
        name: `PV${i}`,
        data: data[currentKey] || [],
        format: (val) => val.toFixed(2) + "A"
      });

      pvVoltageSeries.push({
        name: `PV${i}电压`,
        data: data[voltageKey] || [],
        format: (val) => val.toFixed(2) + "V"
      });
    }
  }

  return {
    categories: formattedTimes,
    series: pvCurrentSeries.length > 0 ? pvCurrentSeries : [
      {
        name: "PV1",
        data: [],
        format: (val) => val.toFixed(2) + "A"
      },
      {
        name: "PV2",
        data: [],
        format: (val) => val.toFixed(2) + "A"
      },
      {
        name: "PV3",
        data: [],
        format: (val) => val.toFixed(2) + "A"
      }
    ],
    voltage: pvVoltageSeries.length > 0 ? pvVoltageSeries : [
      {
        name: "PV1电压",
        data: [],
        format: (val) => val.toFixed(2) + "V"
      },
      {
        name: "PV2电压",
        data: [],
        format: (val) => val.toFixed(2) + "V"
      },
      {
        name: "PV3电压",
        data: [],
        format: (val) => val.toFixed(2) + "V"
      }
    ]
  };
};

// 处理交流图表数据
const processACChartData = (data, formattedTimes) => {
  return {
    categories: formattedTimes,
    series: [
      {
        name: "A相电流",
        data: data.aphaseIa || [],
        format: (val) => val.toFixed(2) + "A"
      },
      {
        name: "B相电流",
        data: data.bphaseIb || [],
        format: (val) => val.toFixed(2) + "A"
      },
      {
        name: "C相电流",
        data: data.cphaseIc || [],
        format: (val) => val.toFixed(2) + "A"
      }
    ],
    voltage: [
      {
        name: "AB相电压",
        data: data.abVoltage || [],
        format: (val) => val.toFixed(2) + "V"
      },
      {
        name: "BC相电压",
        data: data.bcVoltage || [],
        format: (val) => val.toFixed(2) + "V"
      },
      {
        name: "CA相电压",
        data: data.caVoltage || [],
        format: (val) => val.toFixed(2) + "V"
      }
    ]
  };
};

// 创建空图表数据
const createEmptyPowerData = () => {
  return {
    categories: [],
    series: [
      {
        name: "总直流功率",
        data: []
      },
      {
        name: "总有功功率",
        data: []
      }
    ]
  };
};

const createEmptyDCData = () => {
  return {
    categories: [],
    series: [
      {
        name: "PV1",
        data: [],
        format: (val) => val.toFixed(2) + "A"
      },
      {
        name: "PV2",
        data: [],
        format: (val) => val.toFixed(2) + "A"
      },
      {
        name: "PV3",
        data: [],
        format: (val) => val.toFixed(2) + "A"
      }
    ],
    voltage: [
      {
        name: "PV1电压",
        data: [],
        format: (val) => val.toFixed(2) + "V"
      },
      {
        name: "PV2电压",
        data: [],
        format: (val) => val.toFixed(2) + "V"
      },
      {
        name: "PV3电压",
        data: [],
        format: (val) => val.toFixed(2) + "V"
      }
    ]
  };
};

const createEmptyACData = () => {
  return {
    categories: [],
    series: [
      {
        name: "A相电流",
        data: [],
        format: (val) => val.toFixed(2) + "A"
      },
      {
        name: "B相电流",
        data: [],
        format: (val) => val.toFixed(2) + "A"
      },
      {
        name: "C相电流",
        data: [],
        format: (val) => val.toFixed(2) + "A"
      }
    ],
    voltage: [
      {
        name: "AB相电压",
        data: [],
        format: (val) => val.toFixed(2) + "V"
      },
      {
        name: "BC相电压",
        data: [],
        format: (val) => val.toFixed(2) + "V"
      },
      {
        name: "CA相电压",
        data: [],
        format: (val) => val.toFixed(2) + "V"
      }
    ]
  };
};

// 获取颜色配置
export const getChartColors = (index, legendItems, acPhaseColors, legendColors, tabIndex, visibleLegendItems) => {
  if (tabIndex === 2) {
    // 交流数据使用自定义颜色
    const legendItemName = legendItems[index];
    return acPhaseColors[legendItemName] || legendColors[index];
  } else if (tabIndex === 1) {
    // PV数据使用原有颜色逻辑
    const pvNumber = parseInt(visibleLegendItems[index].replace('PV', '')) - 1;
    return legendColors[pvNumber % legendColors.length];
  }
  // 其他情况使用默认颜色
  return legendColors[index % legendColors.length];
};
