import request from '@/utils/request'


//获取电站列表
export function getStationList() {
    return request({
        url: '/app/stationListData',
        method: 'get'
    })
}

// 获取电站详情
export function getStationDetail(stationId) {
    return request({
        url: `/app/stationDetailData`,
        method: 'get',
        params: {stationId},
    });
}


//获取电站简介
export function getStationDetailinfo(stationId) {
    return request({
        url: `/app/stationInfoDetailData`,
        method: 'get',
        params: {stationId},
    })

}

//获取逆变器
export function getEquipment(stationId) {
    return request({
        url: `/app/equipmentListData`,
        method: 'get',
        params: {stationId},
    })

}

// 获取电站故障
export function getEquipmentWarning(stationId = null) {
  const params = stationId ? { stationId } : {}; 
  return request({
    url: '/app/equipmentWarningData',
    method: 'get',
    params: params, // 根据是否传递了 stationId 来决定请求参数
  });
}


//电站等等效时
export function getAllStationDaily() {
    return request({
        url: `/app/allStationDailyData`,
        method: 'get',
    })
}

// 获取特定日期的电站数据
export function getStationDataByDateParams(year, month, day) {
    return request({
        url: `/app/allStationDailyData?year=${year}&month=${month}&day=${day}`, // 传递年、月、日作为查询参数
        method: 'get',
    });
}

//提供总数据
export function getAllStationData() {
    return request({
        url: `/app/allStationData`,
        method: 'get',
    })
}

//提供电站发电趋势的数据，包含当日和历史
export function getStationCurveData(params) {
    return request({
        url: '/app/stationCurveData',
        method: 'get',
        params
    })
}


// 修改电站信息
export function postStationInfo(data) {
    return request({
        url: '/app/postStationInfo',
        method: 'post',
        data,  // 传递请求体中的数据
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token') || '{{token}}'}` // 默认 token 如果没有
        }
    });
}







// 获取设备概览数据
export function getEquipmentCurveData(equipmentId, params = {}) {
    const { year, month, day, dataType} = params;
    return request({
        url: '/app/equipmentCurveData',
        method: 'get',
        params: {
            equipmentId,  // 必需
            year,        // 可选
            month,       // 可选
            day,         // 可选
            dataType,
        }
    });
}
