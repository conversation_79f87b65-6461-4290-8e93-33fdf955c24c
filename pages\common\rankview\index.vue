<template>
  <view class="ranking-box">
    <!-- 背景图 -->
    <view class="background-image"></view>
    <!-- 时间选择器 -->
    <view class="date-picker">
      <view class="date-controls">
        <uni-icons type="left" size="18" @click="changeDate(-1)" />
        <picker mode="date" @change="onDateChange" :value="dateValue" :end="maxDate">
          <view class="selected-date">
            {{ dateValue }}
          </view>
        </picker>
        <uni-icons type="right" size="18" @click="changeDate(1)" />
      </view>
      <!-- <uni-icons type="list" size="18" @click="sortList" class="sort-icon" /> -->
    </view>
    <!-- 排行榜 -->
    <view class="ranking-list">
      <view class="ranking-item" v-for="(item, index) in stationList" :key="index">
        <text class="rank">{{ index + 1 }}.</text>
        <image
          :src="'/static/gree.png'"
          class="device-image"
          @error="handleImageError"
          mode="aspectFit"
        />
        <view class="ranking-content">
          <text class="company-name" @longpress="showFullName(item.fullName)">{{ item.fullName }}</text>
          <view class="progress-wrapper">
            <view class="progress-dot"></view>
            <beautiful-progress
              :percentage="calculateProgress(item.equivalentTime)"
              barColor="#4cd964"
              backgroundColor="#e0e0e0"
              height="24"
              type="striped"
              :animated="true"
              :borderRadius="10"
            ></beautiful-progress>
          </view>
        </view>
        <text class="time">{{ item.equivalentTime }}h</text>
      </view>
    </view>
  </view>
</template>

<script>
import {
  getAllStationDaily,
  getStationDataByDateParams,
} from "@/api/station/station.js";
import BeautifulProgress from '@/components/common/BeautifulProgress.vue';

export default {
  components: {
    BeautifulProgress
  },
  data() {
    return {
      stationList: [],
      dateValue: this.getCurrentDate(),
      maxDate: this.getCurrentDate(), // 最大可选日期为今天
    };
  },
  mounted() {
    this.getAllStationDaily();
    this.fetchStationData(); // 初始化获取数据
  },
  methods: {
    getAllStationDaily() {
      getAllStationDaily({
        date: this.selectedDate,
      }).then((res) => {
        this.stationList = res.rows;
        ("日期数值监测", this.stationList);
      });
    },
    changeDate(days) {
      const currentDate = new Date(this.dateValue);
      currentDate.setDate(currentDate.getDate() + days);

      // 添加日期限制逻辑
      const maxDate = new Date(this.maxDate);
      if (days > 0 && currentDate > maxDate) {
        // 如果向后翻日期超过最大日期，则不执行
        uni.showToast({
          title: '不能超过当前日期',
          icon: 'none'
        });
        return;
      }

      this.dateValue = `${currentDate.getFullYear()}-${String(
        currentDate.getMonth() + 1
      ).padStart(2, "0")}-${String(currentDate.getDate()).padStart(2, "0")}`;
      this.fetchStationData(); // 调用获取数据的方法
    },
    getCurrentDate() {
      const today = new Date();
      return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    },
    onDateChange(event) {
      this.dateValue = event.detail.value;
      this.fetchStationData(); // 调用获取数据的方法
    },
    fetchStationData() {
      const [year, month, day] = this.dateValue.split("-").map(Number); // 提取年、月、日
      getStationDataByDateParams(year, month, day).then((res) => {
        this.stationList = res.rows; // 更新 stationList
        ("根据日期获取的数据", this.stationList);
      });
    },
    calculateProgress(equivalentTime) {
      // 最大值为 8 小时，进度条的最大值为 100%
      const maxTime = 8;
      const progress = (equivalentTime / maxTime) * 100;
      return Math.min(progress, 100); // 确保不超过 100%
    },
    sortList() {
      // 实现排序逻辑
    },
    // 图片加载失败时的处理函数
    handleImageError(e) {
      // 当图片加载失败时，将其替换为默认图片
      e.target.src = '/static/gree.png'
    },
    // 显示完整公司名称
    showFullName(name) {
      uni.showToast({
        title: name,
        icon: 'none',
        duration: 2000
      });
    }
  },
};
</script>

<style scoped>
/* 整体容器样式 */
.ranking-box {
  background: #f8f9fa;
  min-height: 100vh;
}

/* 背景图优化 */
.background-image {
  background-image: url("@/static/images/index/sort.png");
  background-size: cover;
  background-position: center;
  width: 100%;
  height: 200rpx;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}


/* 日期选择器美化 */
.date-picker {
  background: #ffffff;
  margin: -30rpx 30rpx 30rpx;
  border-radius: 10rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1rpx solid #f0f0f0;
}

.date-controls {
  display: flex;
  align-items: center;
  position: relative;
  flex: 1;
  justify-content: center;
}

.selected-date {
  margin: 0 20rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.sort-icon {
  padding: 10rpx;
  background: #f0f2f5;
  border-radius: 50%;
  cursor: pointer;
  position: absolute;
  right: 0;
}

/* 排行榜列表容器 */
.ranking-list {
  margin: 0 30rpx;
  background: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
  overflow: hidden;
  border: 1rpx solid #f0f0f0;
}

/* 排行项样式 */
.ranking-item {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  height: 120rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

/* 排名数字样式 */
.rank {
  font-size: 32rpx;
  font-weight: bold;
  color: #666;
  width: 60rpx;
  text-align: center;
}

/* 前三名特殊样式 */
.ranking-item:nth-child(1) .rank {
  color: #f0a54a;
}

.ranking-item:nth-child(2) .rank {
  color: #a0a0a0;
}

.ranking-item:nth-child(3) .rank {
  color: #c87f32;
}

/* 设备图片样式 */
.device-image {
  width: 80rpx;
  height: 60rpx;
  border-radius: 6rpx;
  object-fit: contain;
  margin: 0 16rpx;
  background-color: #f8f8f8;
  flex-shrink: 0;
}

/* 排行内容区域 */
.ranking-content {
  flex: 1;
  margin-left: 10rpx;
  padding: 0 10rpx;
  width: 0; /* 确保内容区域可以收缩 */
}

.company-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  display: block;
}

.progress-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
}

.progress-dot {
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  background-color: #4cd964;
  margin-right: 8rpx;
  flex-shrink: 0;
}

/* 进度条样式优化 */
.u-line-progress {
  height: 16rpx !important;
  border-radius: 8rpx !important;
  overflow: hidden;
  background: #e9f5eb !important;
  flex: 1;
}

.u-line-progress .u-line-progress-bg {
  background: #e9f5eb !important;
  border-radius: 8rpx !important;
}

.u-line-progress .u-line-progress-fill {
  background: #4cd964 !important;
  border-radius: 8rpx !important;
  transition: width 0.3s ease;
}

/* 时间显示 */
.time {
  font-size: 28rpx;
  color: #4cd964;
  font-weight: 600;
  width: 80rpx;
  text-align: right;
  margin-left: 16rpx;
  flex-shrink: 0;
}

/* 响应式调整 */
@media screen and (max-width: 600px) {
  .ranking-box {
    padding-bottom: 30rpx;
  }

  .background-image {
    height: 240rpx;
  }

  .date-picker {
    margin: 15rpx 20rpx 15rpx;
    padding: 20rpx;
  }

  .ranking-list {
    margin: 0 20rpx;
  }

  .ranking-item {
    padding: 20rpx;
    height: 100rpx;
  }

  .device-image {
    width: 100rpx;
    height: 60rpx;
  }

  .company-name {
    font-size: 26rpx;
  }

  .time {
    font-size: 26rpx;
  }
}
</style>
