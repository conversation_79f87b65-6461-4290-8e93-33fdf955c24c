<template>
  <view class="login-container">
    <!-- 顶部图片区域 -->
    <view class="top-image">
      <image
        src="../static/images/login/login-bg.jpg"
        mode="aspectFill"
        class="illustration"
      ></image>
    </view>

    <!-- 登录框 -->
    <view class="login-box">
      <!-- Logo和标题 -->
      <view class="header">
        <!-- <image src="../static/demo1.png" class="logo" mode="aspectFit"/> -->
        <text class="title">登录</text>
      </view>

      <!-- 登录表单 -->
      <view class="login-form">
        <!-- 用户名输入框 -->
        <view class="input-group">
          <text class="input-label">账号</text>
          <view class="input-with-icon">
            <view class="iconfont icon-user icon"></view>
            <input
              v-model="loginForm.username"
              class="input"
              type="text"
              placeholder="请输入账号"
              maxlength="30"
              placeholder-class="placeholder"
            />
          </view>
        </view>

        <!-- 密码输入框 -->
        <view class="input-group">
          <text class="input-label">密码</text>
          <view class="input-with-icon">
            <view class="iconfont icon-password icon"></view>
            <input
              v-model="loginForm.password"
              type="password"
              class="input"
              placeholder="请输入密码"
              maxlength="20"
              placeholder-class="placeholder"
            />
          </view>
        </view>

        <!-- 验证码 -->
        <view class="input-group verification" v-if="captchaEnabled">
          <text class="input-label">验证码</text>
          <view class="verification-box">
            <view class="input-with-icon captcha-input-box">
              <view class="iconfont icon-code icon"></view>
              <input
                v-model="loginForm.code"
                type="number"
                class="input captcha-input"
                placeholder="请输入验证码"
                maxlength="4"
                placeholder-class="placeholder"
              />
            </view>
            <view class="captcha-image-box">
              <image
                :src="codeUrl"
                @click="getCode"
                class="captcha-image"
              ></image>
            </view>
          </view>
        </view>

        <!-- 登录按钮 -->
        <button class="login-btn" @click="handleLogin" :loading="loading">
          登 录
        </button>

        <!-- 底部链接 -->
        <view class="bottom-links">
          <!-- <text class="link-text" @tap="handleUserRegister">注册账号</text> -->
          <!-- <text class="link-text" @tap="handlePrivacy">隐私政策</text> -->
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getCodeImg } from "@/api/login";

export default {
  data() {
    return {
      loading: false,
      codeUrl: "",
      captchaEnabled: true,
      register: false,
      globalConfig: getApp().globalData.config,
      loginForm: {
        username: "admin",
        password: "admin123",
        code: "",
        uuid: "",
      },
    };
  },
  created() {
    this.getCode();
  },
  methods: {
    // 用户注册
    handleUserRegister() {
      this.$tab.redirectTo(`/pages/register`);
    },
    // 隐私协议
    handlePrivacy() {
      let site = this.globalConfig.appInfo.agreements[0];
      this.$tab.navigateTo(
        `/pages/common/webview/index?title=${site.title}&url=${site.url}`
      );
    },
    // 用户协议
    handleUserAgrement() {
      let site = this.globalConfig.appInfo.agreements[1];
      this.$tab.navigateTo(
        `/pages/common/webview/index?title=${site.title}&url=${site.url}`
      );
    },
    // 获取图形验证码
    getCode() {
      getCodeImg().then((res) => {
        this.captchaEnabled =
          res.captchaEnabled === undefined ? true : res.captchaEnabled;
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.loginForm.uuid = res.uuid;
        }
      });
    },
    // 登录方法
    async handleLogin() {
      if (this.loginForm.username === "") {
        this.$modal.msgError("请输入您的账号");
      } else if (this.loginForm.password === "") {
        this.$modal.msgError("请输入您的密码");
      } else if (this.loginForm.code === "" && this.captchaEnabled) {
        this.$modal.msgError("请输入验证码");
      } else {
        this.loading = true;
        this.$modal.loading("登录中，请耐心等待...");
        try {
          await this.pwdLogin();
        } finally {
          this.loading = false;
        }
      }
    },
    // 密码登录
    async pwdLogin() {
      this.$store
        .dispatch("Login", this.loginForm)
        .then(() => {
          this.$modal.closeLoading();
          this.loginSuccess();
        })
        .catch(() => {
          if (this.captchaEnabled) {
            this.getCode();
          }
        });
    },
    // 登录成功后，处理函数
    loginSuccess(result) {
      // 设置用户信息
      this.$store.dispatch("GetInfo").then((res) => {
        this.$tab.reLaunch("/pages/index");
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.top-image {
  height: 400rpx;
  overflow: hidden;

  .illustration {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.login-box {
  flex: 1;
  margin-top: -50rpx;
  background: #fff;
  border-radius: 40rpx 40rpx 0 0;
  padding: 50rpx 40rpx;
  position: relative;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.header {
  text-align: center;
  margin-bottom: 60rpx;

  .logo {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 20rpx;
  }

  .title {
    font-size: 36rpx;
    color: #333;
    font-weight: 600;
  }
}

.login-form {
  .input-group {
    margin-bottom: 30rpx;

    .input-label {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 16rpx;
      display: block;
    }

    .input-with-icon {
      display: flex;
      align-items: center;
      background: #f5f7fa;
      border-radius: 45rpx;
      padding: 0 40rpx;

      .icon {
        font-size: 38rpx;
        color: #999;
        margin-right: 20rpx;
      }

      .input {
        flex: 1;
        height: 90rpx;
        font-size: 30rpx;
        color: #333;
        background: transparent;
      }
    }

    &.verification {
      .verification-box {
        display: flex;
        align-items: center;
        gap: 20rpx;

        .captcha-input-box {
          flex: 1;
        }

        .captcha-image-box {
          width: 200rpx;
          height: 90rpx;
          border-radius: 45rpx;
          overflow: hidden;

          .captcha-image {
            width: 100%;
            height: 100%;
            transform: scale(1.2);
            transform-origin: center;
          }
        }
      }
    }
  }
}

.login-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background: linear-gradient(45deg, #4caf50, #45a049);
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 45rpx;
  margin-top: 60rpx;
  border: none;
  transition: all 0.3s;

  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }
}

.bottom-links {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40rpx;
  padding: 0 20rpx;

  .link-text {
    font-size: 28rpx;
    color: #666;

    &:active {
      opacity: 0.7;
    }
  }
}

.placeholder {
  color: #999;
  font-size: 28rpx;
}

/* H5适配 */
// #ifdef H5
.login-container {
  height: 100vh;
}

.login-box {
  overflow-y: auto;
}
// #endif
</style>
