<template>
  <view class="charts-box">
    <!-- 图表标题和切换按钮 -->
    <view class="chart-header">
      <view class="chart-title">
        {{ title }}
      </view>
      <!-- <uni-icons type="tune" size="20" class="chart-toggle-btn" @click="toggleChartType"></uni-icons> -->
    </view>

    <!-- 图表主体 -->
    <view class="chart-box">
      <qiun-data-charts :type="chartType" :opts="computedChartOpts" :chartData="chartData" />
      <!-- 动态显示单位 -->
      <view class="y-axis-label-left">
        {{ leftAxisLabel }}
      </view>
      <view class="y-axis-label-right" v-if="showRightAxis">
        {{ rightAxisLabel }}
      </view>
    </view>

    <!-- 图表图例 -->
    <view class="chart-legend">
      <view v-for="(item, index) in legendItems" :key="index" class="legend-item">
        <view class="legend-color" :style="{ backgroundColor: getLegendColor(item) }"></view>
        <view class="legend-text">{{ item }}</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "InverterChart",
  props: {
    // 图表标题
    title: {
      type: String,
      default: "",
    },
    // 图表数据
    chartData: {
      type: Object,
      default: () => ({
        categories: [],
        series: [],
      }),
    },
    // 左侧Y轴标签
    leftAxisLabel: {
      type: String,
      default: "",
    },
    // 右侧Y轴标签
    rightAxisLabel: {
      type: String,
      default: "",
    },
    // 是否显示右侧Y轴
    showRightAxis: {
      type: Boolean,
      default: false,
    },
    // 图例项
    legendItems: {
      type: Array,
      default: () => [],
    },
    // 图例颜色（新增）
    legendColors: {
      type: Array,
      default: () => ["#40b780", "#1890ff", "#f5a623", "#8c6ac4", "#ee8882"],
    },
  },
  data() {
    return {
      chartType: "line",
      chartOpts: {
        padding: [45, 15, 10, 15],
        dataLabel: false,
        dataPointShape: true,
        enableScroll: false,
        animation: true,
        duration: 1000,
        timing: 'easeInOut',
        rotate: false,
        rotateLock: false,
        fontSize: 13,
        fontColor: "#666666",
        background: "#FFFFFF",
        pixelRatio: 1,
        width: 375,
        height: 500,
        legend: {
          show: false,
        },
        xAxis: {
          disableGrid: true,
          boundaryGap: true,
          fontColor: "#666666",
          fontSize: 13,
          labelCount: 6,
          axisLine: true,
          axisLineColor: "#CCCCCC",
        },
        yAxis: {
          gridType: "dash",
          dashLength: 4,
          splitNumber: 5,
          gridColor: "#EEEEEE",
          fontColor: "#666666",
          fontSize: 13,
          data: [
            {
              min: 0,
            },
          ],
        },
        extra: {
          line: {
            type: "curve",
            width: 3,
            activeType: "hollow",
            linearType: "custom",
            onShadow: true,
            shadowColor: "rgba(0,0,0,0.2)",
            shadowBlur: 4,
            shadowOffsetY: 2,
          },
          area: {
            type: "curve",
            opacity: 0.2,
            addLine: true,
            width: 3,
            gradient: true,
            activeType: "hollow",
          },
          tooltip: {
            showBox: true,
            showArrow: true,
            showCategory: true,
            borderWidth: 0,
            borderRadius: 8,
            borderColor: "#40b780",
            borderOpacity: 0.1,
            bgColor: "#40b780",
            bgOpacity: 0.9,
            gridType: "dash",
            dashLength: 4,
            gridColor: "#CCCCCC",
            fontColor: "#FFFFFF",
            splitLine: true,
            horizentalLine: false,
            xAxisLabel: false,
            yAxisLabel: false,
            labelBgColor: "#FFFFFF",
            labelBgOpacity: 0.9,
            labelFontColor: "#666666",
            fontSize: 12,
            padding: 5,
            itemGap: 6,
            boxWidth: 'auto',
            boxHeight: 'auto',
            position: 'right',
            format: function (item) {
              return item.name + ' ' + item.data;
            }
          },
          markLine: {
            type: "dash",
            dashLength: 5,
            data: [],
          },
        },
      },
      colorMap: {
        'PV1': '#2ca02c', // 绿色
        'PV2': '#1f77b4', // 蓝色
        'PV3': '#ff7f0e', // 橙色
        'PV4': '#9467bd', // 紫色
        'PV5': '#d62728', // 红色
        'PV6': '#8c564b', // 棕色
        'PV7': '#e377c2', // 粉色
        'PV8': '#7f7f7f', // 灰色
        'PV9': '#bcbd22', // 黄绿色
        'PV10': '#17becf', // 青色
        'PV11': '#aec7e8', // 浅蓝色
        'PV12': '#ffbb78', // 浅橙色
        'PV13': '#98df8a', // 浅绿色
        'PV14': '#ff9896', // 浅红色
        'PV15': '#c5b0d5', // 浅紫色
        'PV16': '#c49c94', // 浅棕色
        'PV17': '#f7b6d2', // 浅粉色
        'PV18': '#c7c7c7', // 浅灰色
        '总直流功率': '#40b780',  // 绿色
        '总有功功率': '#1890ff',  // 蓝色
        'A相电流': '#FF0000',  // 红色
        'B相电流': '#FFD700',  // 金黄色
        'C相电流': '#0000FF',  // 蓝色
        'AB相电压': '#FF0000',  // 红色
        'BC相电压': '#FFD700',  // 金黄色
        'CA相电压': '#0000FF',  // 蓝色
      }
    };
  },
  computed: {
    computedChartOpts() {
      const opts = JSON.parse(JSON.stringify(this.chartOpts));

      if (this.chartData && this.chartData.series) {
        const colors = this.chartData.series.map(item => {
          const name = item.name || '';
          return this.getLegendColor(name);
        });

        if (colors.length > 0) {
          opts.color = colors;
        }
      }

      // 根据 showRightAxis 属性动态设置 Y 轴数据
      const yAxisData = [
        {
          min: 0,
          position: "left",
          title: this.leftAxisLabel,
        },
      ];

      if (this.showRightAxis) {
        yAxisData.push({
          min: 0,
          position: "right",
          title: this.rightAxisLabel,
        });
      }

      opts.yAxis.data = yAxisData;
      return opts;
    },
  },
  methods: {
    toggleChartType() {
      this.chartType = this.chartType === "line" ? "area" : "line";
      this.$emit("chart-type-changed", this.chartType);
    },

    // 新增方法：根据图例项获取对应颜色
    getLegendColor(name) {
      // 直接检查 colorMap 中是否存在对应的颜色
      if (this.colorMap[name]) {
        return this.colorMap[name];
      }

      // 处理电压数据，例如 "PV1电压" 需要匹配 "PV1" 的颜色
      const voltageMatch = name.match(/(.+)电压/);
      if (voltageMatch && this.colorMap[voltageMatch[1]]) {
        return this.colorMap[voltageMatch[1]];
      }

      // 如果是 PV 开头的名称
      const pvMatch = name.match(/PV(\d+)/);
      if (pvMatch) {
        return this.colorMap[`PV${pvMatch[1]}`];
      }

      return '#2ca02c'; // 默认颜色
    },
  },
};
</script>

<style lang="scss" scoped>
.charts-box {
  width: 100%;
  padding: 15rpx 0;
  position: relative;

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20rpx 15rpx;
    margin-bottom: 15rpx;
    border-bottom: 1rpx dashed rgba(0, 0, 0, 0.05);

    .chart-title {
      font-size: 30rpx;
      font-weight: bold;
      color: #333;
      background: linear-gradient(to right, #40b780, #3fc8ae);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
      letter-spacing: 1rpx;
    }

    .chart-toggle-btn {
      cursor: pointer;
      color: #40b780;
    }
  }

  .chart-box {
    width: 100%;
    height: 45vh;
    position: relative;
    margin: 20rpx 0;

    .y-axis-label-left {
      position: absolute;
      left: 15rpx;
      top: 15rpx;
      font-size: 24rpx;
      color: #666;
      background-color: rgba(255, 255, 255, 0.8);
      padding: 6rpx 12rpx;
      border-radius: 6rpx;
      box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
    }

    .y-axis-label-right {
      position: absolute;
      right: 15rpx;
      top: 15rpx;
      font-size: 24rpx;
      color: #666;
      background-color: rgba(255, 255, 255, 0.8);
      padding: 6rpx 12rpx;
      border-radius: 6rpx;
      box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
    }
  }

  .chart-legend {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    padding: 15rpx 10rpx;
    margin-top: 20rpx;
    background: linear-gradient(to right, #f8f8f8, #ffffff);
    border-radius: 12rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);

    .legend-item {
      display: flex;
      align-items: center;
      margin: 8rpx 20rpx;
      min-width: 140rpx;
      padding: 8rpx 16rpx;
      border-radius: 30rpx;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(64, 183, 128, 0.05);
      }

      .legend-color {
        width: 30rpx;
        height: 16rpx;
        border-radius: 6rpx;
        margin-right: 10rpx;
        border: 1rpx solid rgba(0, 0, 0, 0.1);
        box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
      }

      .legend-text {
        font-size: 24rpx;
        color: #555;
        font-weight: 500;
      }
    }
  }
}
</style>