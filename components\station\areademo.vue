<template>
  <view class="charts-box">
    <!-- 子组件接收数据并渲染图表 -->
    <qiun-data-charts 
      type="column" 
      :opts="opts" 
      :chartData="chartData"
    />
  </view>
</template>

<script>
export default {
  props: {
    categories: {
      type: Array,
      default: () => []
    },
    series: {
      type: Array,
      default: () => []
    },
    unit: {
      type: String,
      default: '功率 (KWh)'
    }
  },
  data() {
    return {
      chartData: {},  // 用来存储传递过来的图表数据
      opts: {
        color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"],
        padding: [15, 15, 0, 5],
        enableScroll: false,
        legend: {},
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          data: [
            { min: 0 }
          ]
        },
        extra: {
          column: {
            type: "group",
            width: 30,
            activeBgColor: "#000000",
            activeBgOpacity: 0.08
          }
        }
      }
    };
  },
  watch: {
    categories: {
      handler(newVal) {
        this.updateChart();
      },
      deep: true
    },
    series: {
      handler(newVal) {
        this.updateChart();
      },
      deep: true
    }
  },

 methods: {
   updateChart() {
     if (!this.categories || !this.series) return;
     // 转换时间格式
     const formattedCategories = this.categories.map(val => {
       if (typeof val === 'string' && /^\d{3,4}$/.test(val)) {
         const hour = val.length === 3 ? val[0] : val.slice(0, 2);
         const minute = val.length === 3 ? val.slice(1) : val.slice(2);
         return `${hour}:${minute}`;
       }
       return val;
     });
     
     this.chartData = {
       categories: formattedCategories,
       series: this.series
     };
   }
 },

  mounted() {
    this.updateChart();
  }
};
</script>

<style scoped>
.charts-box {
  width: 100%;
  height: 300px;
}
</style>
