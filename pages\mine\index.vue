<template>
	<view class="mine-container">
		<!--顶部个人信息栏-->
		<view class="header-section">
			<view class="flex padding justify-between">
				<view class="flex align-center">
					<view v-if="!avatar" class="cu-avatar xl round bg-white">
						<view class="iconfont icon-people text-gray icon"></view>
					</view>
					<image v-if="avatar" @click="handleToAvatar" :src="avatar" class="cu-avatar xl round"
						mode="widthFix">
					</image>
					<view v-if="!name" @click="handleToLogin" class="login-tip">
						点击登录
					</view>
					<view v-if="name" class="user-info">
						<view class="u_title">
							{{ name }}
						</view>
						<view class="u_sub_title">
							{{ name }}
						</view>
						<!-- <view class="u_sub_title">
              组织代码<view class="iconfont icon-about_o"></view>：CHN5MAAF<view
                class="iconfont icon-copy"
                @click="copy('CHN5MAAF')"
              ></view>
            </view> -->
						<view class="u_sub_title">
							账号昵称：{{ user.nickName }}
							<view></view>
						</view>
					</view>
				</view>
				<view @click="handleToInfo" class="flex align-center">
					<view class="iconfont icon-right"></view>
				</view>
			</view>
		</view>

		<view style="padding: 20rpx">
			<view class="content-section">
				<view @click="clickBannerItem(image)" class="image">
					<image class="image" :src="image" mode="aspectFill" />
				</view>
			</view>
			<CardBox>
				<u-cell-group :border="false">
					<u-cell icon="/static/images/mine/security.png" title="账号与安全" :isLink="true" :border="false"
						:clickable="false" arrow-direction="right" url="/pages/mine/info/edit"></u-cell>
				</u-cell-group>
			</CardBox>
			<!--			<CardBox>-->
			<!--				<u-cell-group :border="false">-->
			<!--					<u-cell icon="/static/images/mine/notification.png" title="通知" :isLink="true" arrow-direction="right"-->
			<!--						url="/pages/mine/info/edit"></u-cell>-->
			<!--					<u-cell icon="/static/images/mine/adjustment.png" title="通用" :isLink="true" arrow-direction="right"-->
			<!--						url="/pages/mine/help/index"></u-cell>-->
			<!--					<u-cell icon="/static/images/mine/report.png" title="报表推送" :isLink="true" :border="false"-->
			<!--						arrow-direction="right" url="/pages/mine/about/index"></u-cell>-->
			<!--				</u-cell-group>-->
			<!--			</CardBox>-->
			<!--			<CardBox>-->
			<!--				<u-cell-group :border="false">-->
			<!--					<u-cell icon="/static/images/mine/description.png" title="使用声明" :isLink="true" arrow-direction="right"-->
			<!--						url="/pages/mine/info/edit"></u-cell>-->
			<!--					<u-cell icon="/static/images/mine/sharing.png" title="应用分享" :isLink="true" arrow-direction="right"-->
			<!--						url="/pages/mine/info/edit"></u-cell>-->
			<!--					<u-cell icon="/static/images/mine/about.png" title="关于" :isLink="true" :border="false"-->
			<!--						arrow-direction="right" url="/pages/mine/help/index"></u-cell>-->
			<!--				</u-cell-group>-->
			<!--			</CardBox>-->
			<CardBox>
				<view style="text-align: center" @click="handleLogout"> 退出 </view>
			</CardBox>
		</view>
	</view>
</template>

<script>
	import storage from "@/utils/storage";
	import CardBox from "@/components/CardBox/CardBox.vue";
	import {
		getUserProfile
	} from "@/api/system/user"

	export default {
		components: {
			CardBox,
		},
		data() {
			return {
				user: {},
				name: this.$store.state.user.name,
				version: getApp().globalData.config.appInfo.version,
				current: 0,
				swiperDotIndex: 0,
				image: "/static/images/banner/banner01.jpg",
			};
		},
		computed: {
			avatar() {
				return this.$store.state.user.avatar;
			},
			windowHeight() {
				return uni.getSystemInfoSync().windowHeight - 50;
			},
		},
		onLoad() {
			this.getUser();
		},
		methods: {
			getUser() {
			    getUserProfile().then(response => {
			        this.user = response.data
			    })
			},
			handleToInfo() {
				this.$tab.navigateTo("/pages/mine/info/index");
			},
			handleToLogin() {
				this.$tab.reLaunch("/pages/login");
			},
			handleToAvatar() {
				this.$tab.navigateTo("/pages/mine/avatar/index");
			},
			handleLogout() {
				this.$modal.confirm("确定注销并退出系统吗？").then(() => {
					this.$store.dispatch("LogOut").then(() => {
						this.$tab.reLaunch("/pages/index");
					});
				});
			},
			clickBannerItem(item) {
				console.info(item);
			},
			copy(text) {
				uni.setClipboardData({
					data: text,
					success: function() {
						// 可以添加用户友好的提示，例如使用uni.showToast提示复制成功
						uni.showToast({
							title: "复制成功",
							icon: "success",
							duration: 2000,
						});
					},
					fail: function() {
						uni.showToast({
							title: "复制失败",
							icon: "error",
							duration: 2000,
						});
						// 可以添加错误处理或用户友好的提示
					},
				});
			},
		},
	};
</script>

<style lang="scss">
	page {
		background-color: #f5f6f7;
	}

	.mine-container {
		width: 100%;
		height: 100%;

		.header-section {
			padding: 0px 15px 0px 15px;
			background-color: white;

			.login-tip {
				font-size: 18px;
				margin-left: 10px;
			}

			.cu-avatar {
				border: 2px solid #eaeaea;

				.icon {
					font-size: 40px;
				}
			}

			.user-info {
				margin-left: 15px;

				.u_title {
					font-weight: bold;
					font-size: 18px;
				}

				.u_sub_title {
					margin-top: 5px;
					color: gray;
				}
			}
		}

		.content-section {
			width: 100%;
			height: 180rpx;
			margin-bottom: 20rpx;

			.image {
				border-radius: 20rpx;
				height: 100%;
				width: 100%;
			}
		}

		.menu-list {
			border-radius: 20rpx;
			padding: 10rpx;
			background-color: #ffffff;
		}

		.logout {
			margin: 15rpx 30rpx;
			padding: 15rpx 0rpx;
			border-radius: 15rpx;
			background-color: white;
		}
	}
</style>