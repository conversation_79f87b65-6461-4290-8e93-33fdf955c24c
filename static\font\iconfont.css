@font-face {
  font-family: 'iconfont';
  src: url('@/static/font/woff.woff') format('woff'),
       url('@/static/font/woff.woff2') format('woff2'),
	   url('@/static/font/iconfont.ttf') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  display: inline-block;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-user:before {
  content: "\e639";
}

.icon-password:before {
  content: "\e63a";
}

.icon-logout:before {
  content: "\e644";
}

.icon-safety:before {
  content: "\e614";
}

.icon-notice:before {
  content: "\e69b";
}

.icon-noticeMain:before {
  content: "\e759";
}

.icon-instruction:before {
  content: "\e69d";
}

.icon-share:before {
  content: "\e6ae";
}

.icon-current:before {
  content: "\e60d";
}

.icon-push:before {
  content: "\e689";
}

.icon-about:before {
  content: "\e637";
}

.icon-about_o:before {
  content: "\eb77";
}

.icon-right:before {
  content: "\e9d0";
}

.icon-copy:before {
  content: "\e65f";
}