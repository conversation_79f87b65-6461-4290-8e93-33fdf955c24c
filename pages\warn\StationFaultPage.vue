<template>
	<view class="container">
		<!-- 导航栏 -->
		<u-navbar back-text="返回" :title="'故障详情'" :placeholder="true" :titleStyle="{
        fontSize: '36rpx',
        fontWeight: 'bold'
      }" @leftClick="navLeftClick" />

		<!-- 故障信息展示区域 -->
		<view class="intro-list-box">
			<CardBox>
				<uni-section title="故障信息" type="line">
					<uni-list>
						<InfoList :items="infoListItems" />
					</uni-list>
				</uni-section>
			</CardBox>

			<CardBox>
				<uni-section title="故障原因" type="line">
					<!-- <uni-list>
						<InfoList :items="ownerInfoItems" />
					</uni-list> -->
					<view style="margin:10rpx 20rpx;">
						<text>{{ faultData.faultReason }}</text>
					</view>
				</uni-section>
			</CardBox>

			<CardBox>
				<uni-section title="处理建议" type="line">
					<!-- <uni-list>
						<InfoList :items="otherInfoItems" />
					</uni-list> -->
					<view style="margin:10rpx 20rpx;">
						<text>{{ faultData.hldMsg }}</text>
					</view>
				</uni-section>
			</CardBox>
		</view>

		<!-- 加载中的占位符 -->
		<view v-if="isLoading" class="loading">
			<text>加载中...</text>
		</view>
	</view>
</template>

<script>
	import CardBox from "@/components/CardBox/CardBox.vue";
	import InfoList from '@/components/list/InfoList.vue';
	import {
		getStationwarning
	} from '@/api/station/equipment.js'

	export default {
		components: {
			CardBox,
			InfoList
		},
		data() {
			return {
				faultData: {
					stationFullName: "",
				}, // 初始化为空
				stationId: '', // 模拟默认故障ID
				isLoading: true, // 加载状态
				infoListItems: [], // 初始化 infoListItems
				ownerInfoItems: [], // 初始化业主信息
				otherInfoItems: [], // 初始化其他信息
				faultId: null, // 存储故障ID
				alarmId: null, // 存储 alarmId 用于接口调用
			};
		},
		mounted() {
			this.initializeInfoListItems();
			if (this.alarmId) {
				this.getStationwarning(this.alarmId); // 使用 alarmId 获取数据
			} else {
				console.error("alarmId 未设置");
			}
		},
		onLoad(options) {
			// 获取传递的 faultId 参数
			this.faultId = options.id || null; // 如果没有传递 faultId，默认为 null
			this.alarmId = this.faultId; // 将 faultId 赋值给 alarmId
			("接收到的 faultId:", this.faultId);
			("设置的 alarmId:", this.alarmId);
		},
		methods: {
			// 添加时间格式化方法
			formatDateTime(dateTimeStr) {
				if (!dateTimeStr) return '--';
				try {
					const date = new Date(dateTimeStr);
					const year = date.getFullYear();
					const month = String(date.getMonth() + 1).padStart(2, '0');
					const day = String(date.getDate()).padStart(2, '0');
					const hours = String(date.getHours()).padStart(2, '0');
					const minutes = String(date.getMinutes()).padStart(2, '0');
					const seconds = String(date.getSeconds()).padStart(2, '0');
					
					return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
				} catch (error) {
					return '--';
				}
			},

			// 初始化各类信息数据
			initializeInfoListItems() {
				this.infoListItems = [{
						title: '所属电站',
						rightText: `${this.faultData.stationName}`,
						clickable: true,
						onClick: () => { // 在这里绑定点击事件
							('设备名称点击');
						}
					},
					{
						title: '设备名称',
						rightText: `${this.faultData.eqName}`
					},
					{
						title: '设备型号',
						rightText: `${this.faultData.model}`
					},
					{
						title: '设备类型',
						rightText: `${this.faultData.deviceType}`
					},
					{
						title: '故障码',
						rightText: `${this.faultData.faultCode}`
					},
					{
						title: '处理状态',
						rightText: `${this.faultData.processStatusStr}`
					},
					{
						title: '发生时间',
						rightText: this.formatDateTime(this.faultData.createTime)
					},
					{
						title: '恢复时间',
						rightText: this.formatDateTime(this.faultData.overTime)
					},
				];

				this.ownerInfoItems = [{
					value: this.faultData.faultReason
				}];
				this.otherInfoItems = [{
					value: this.faultData.faultDesc
				}];
			},

			// 返回上一页
			navLeftClick() {
				uni.navigateBack();
			},

			// 获取故障详情
			getStationwarning(alarmId) {
				return getStationwarning(alarmId)
					.then(response => {
						("API 返回数据：", response.data);
						this.faultData = response.data;
						this.initializeInfoListItems();
						this.isLoading = false;
					})
					.catch(error => {
						console.error("获取故障数据失败：", error);
						this.isLoading = false;
					});
			},
		},
	};
</script>

<style scoped>
	.container {
		display: flex;
		flex-direction: column;
		align-items: stretch;
		padding: 20rpx;
		margin-top: -112rpx;
	}

	.intro-list-box {
		display: flex;
		flex-direction: column;
		margin-top: 20rpx;
	}

	.loading {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 200rpx;
		text-align: center;
		font-size: 36rpx;
	}

	u-navbar {
		background-color: #00A0E9;
	}

	.u-navbar-title {
		color: white;
	}

	/* CardBox 的 flex 布局 */
	uni-section {
		display: flex;
		flex-direction: column;
	}

	uni-list {
		display: flex;
		flex-direction: column;
		margin-top: 10rpx;
	}

	section-title {
		font-size: 28rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
	}
</style>