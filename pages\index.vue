<template>
  <view class="main-content">
    <view class="part-one">
      <swiper class="image-slider" @change="onSlideChange">
        <swiper-item v-for="(item, index) in sliderData" :key="index">
          <view class="part-one-top">
            <view class="data-display">
              <text class="data-title-bold">{{
                currentIndex === 0 ? "当日发电（度）" : "当日收益（元）"
              }}</text>
              <view class="data-num-bold"
                >{{ currentIndex === 0 ? item.dayOutput || "--" : "----" }}
              </view>
            </view>
            <view class="bg-image">
              <image :src="item.image" mode="aspectFit"></image>
            </view>
          </view>

          <view class="part-one-bottom">
            <view class="detailed-indicators">
              <view class="top-item-box">
                <view class="item-title">{{
                  currentIndex === 0 ? "当月发电(万度)" : "当月收益(万元)"
                }}</view>
                <view class="item-number">
                  {{
                    currentIndex === 0 ? formatValue(item.monthOutput) : "----"
                  }}
                </view>
              </view>
              <view class="line"></view>
              <view class="top-item-box">
                <view class="item-title">{{
                  currentIndex === 0 ? "当年发电(万度)" : "当年收益(万元)"
                }}</view>
                <view class="item-number">
                  {{
                    currentIndex === 0 ? formatValue(item.yearOutput) : "----"
                  }}
                </view>
              </view>
              <view class="line"></view>
              <view class="top-item-box">
                <view class="item-title">{{
                  currentIndex === 0 ? "累计发电（万度）" : "累计收益（万元）"
                }}</view>
                <view class="item-number">
                  {{
                    currentIndex === 0 ? formatValue(item.totalOutput) : "----"
                  }}
                </view>
              </view>
            </view>
          </view>

          <view class="part-one-bottom">
            <view class="detailed-indicators">
              <view class="top-item-box">
                <view class="item-title">{{
                  currentIndex === 0 ? "当月发电(万度)" : "当月收益(万元)"
                }}</view>
                <view class="item-number">
                  {{
                    formatValue(
                      currentIndex === 0
                        ? item.monthOutput
                        : item.mcapYield || 0
                    )
                  }}
                </view>
              </view>
              <view class="line"></view>
              <view class="top-item-box">
                <view class="item-title">{{
                  currentIndex === 0 ? "当年发电(万度)" : "当年收益(万元)"
                }}</view>
                <view class="item-number">
                  {{
                    formatValue(
                      currentIndex === 0 ? item.yearOutput : item.ycapYield || 0
                    )
                  }}
                </view>
              </view>
              <view class="line"></view>
              <view class="top-item-box">
                <view class="item-title">{{
                  currentIndex === 0 ? "累计发电（万度）" : "累计收益（万元）"
                }}</view>
                <view class="item-number">
                  {{
                    formatValue(
                      currentIndex === 0
                        ? item.totalOutput
                        : item.tcapYield || 0
                    )
                  }}
                </view>
              </view>
            </view>
          </view>
        </swiper-item>
      </swiper>

      <!-- 自定义指示器 -->
      <view class="indicator-container">
        <view class="indicator-line">
          <view
            v-for="(item, index) in sliderData"
            :key="index"
            :class="['indicator', { active: currentIndex === index }]"
            @click="currentIndex = index"
          ></view>
        </view>
      </view>

      <CardBox class="part-two">
        <view class="chart-area">
          <view class="chart-left">
            <view class="charts-box" style="height: 300rpx">
              <qiun-data-charts
                type="ring"
                :opts="ringOpts"
                :chartData="chartData"
              />
            </view>
          </view>

          <view class="chart-right">
            <view class="ring-num">
              <view class="online" @click="goToList('normal')">
                <view class="sign"></view>
                <view class="status-text">正常</view>
                <view class="station-status-num">{{ alldata.normal }}</view>
                <uni-icons type="right" color="#ababab"></uni-icons>
              </view>
              <view class="abnormal" @click="goToList('abnormal')">
                <view class="sign"></view>
                <view class="status-text">异常</view>
                <view class="station-status-num">{{ alldata.abnormal }}</view>
                <uni-icons type="right" color="#ababab"></uni-icons>
              </view>
              <view class="offline" @click="goToList('offline')">
                <view class="sign"></view>
                <view class="status-text">离线</view>
                <view class="station-status-num">{{ alldata.offline }}</view>
                <uni-icons type="right" color="#ababab"></uni-icons>
              </view>
              <view class="unconnected" @click="goToList('connecting')">
                <view class="sign"></view>
                <view class="status-text">接入中</view>
                <view class="station-status-num">{{ alldata.connecting }}</view>
                <uni-icons type="right" color="#ababab"></uni-icons>
              </view>
            </view>
          </view>
        </view>

        <view class="dividing-line"> </view>
        <view class="detailed-data">
          <view class="top-item-box">
            <view class="item-title"
              >实时功率{{ getPowerUnit(alldata.acPowerAll) }}</view
            >
            <view class="item-number">{{
              formatPowerValue(alldata.acPowerAll)
            }}</view>
          </view>
          <view class="line"></view>
          <view class="top-item-box">
            <view class="item-title">装机功率(MWp)</view>
            <view class="item-number">{{ alldata.capacityAll / 1000000 }}</view>
          </view>
          <view class="line"></view>
          <view class="top-item-box">
            <view class="item-title">功率比</view>
            <view class="item-number"
              >{{ (alldata.powerRatio * 100).toFixed(0) }}%</view
            >
          </view>
        </view>
      </CardBox>
      <TitleCardBox title="发电趋势">
        <view class="trend-date-select">
          <view class="data-subsection">
            <u-subsection activeColor="#40b780" :list="dateSelectList" :current="dateCurrent"
              @change="dateChange"></u-subsection>
          </view>
          <view class="date-num" v-if="showDateControls">
            <uni-icons type="left" size="14" @click="changeDate(-1)"></uni-icons>
            <view class="date-val">{{ formattedDate }}</view>
            <uni-icons v-if="canGoForward" type="right" size="14" @click="changeDate(1)"></uni-icons>
          </view>
        </view>
        <Area style="height: 400rpx; margin-top: 50rpx; right: 20rpx" :categories="parentCategories"
          :series="parentSeries" :trendData="trendData"></Area>
      </TitleCardBox>
    </view>

    <view class="ranking-box">
      <view class="title-container">
        <view class="ranking-title-left">
          <text class="title">日等效小时排行榜</text>
          <!-- <uni-icons
            type="list"
            size="20"
            color="#919191"
            class="icon"
            @click="sortRanking"
          ></uni-icons> -->
        </view>
        <view class="ranking-title-right" @click="goToMore">
          <text class="more-text">更多</text>
          <uni-icons type="right" size="14" color="#999"></uni-icons>
        </view>
      </view>
      <view class="ranking-list">
        <view
          class="ranking-item"
          v-for="(item, index) in stationList.slice(0, 5)"
          :key="index"
        >
          <text class="rank">{{ index + 1 }}.</text>
          <image :src="'/static/gree.png'" class="device-image" />
          <view class="ranking-content">
            <text class="company-name" @longpress="showFullName(item.fullName)">{{ item.fullName }}</text>
            <view class="progress-wrapper">
              <view class="progress-dot"></view>
              <beautiful-progress
                :percentage="calculateProgress(item.equivalentTime)"
                barColor="#4cd964"
                backgroundColor="#e0e0e0"
                height="24"
                type="striped"
                :animated="true"
                :borderRadius="10"
              ></beautiful-progress>
            </view>
          </view>
          <text class="time">{{ item.equivalentTime }}h</text>
        </view>
      </view>
    </view>
    <TitleCardBox title="电站减排">
      <!-- <view class="emission-box"> -->
      <!-- <view class="title">电站减排</view> -->
      <view class="emission-container">
        <view
          class="emission-item"
          style="background-image: url('/static/images/index/tree.png')"
        >
          <text class="number">{{
            (alldata.saveTree / 10000).toFixed(2)
          }}</text>
          <text class="unit">万棵</text>
        </view>
        <view
          class="emission-item"
          style="background-image: url('/static/images/index/co.png')"
        >
          <text class="number">{{
            (alldata.saveCo2 / 1000000).toFixed(2)
          }}</text>
          <text class="unit">千吨</text>
        </view>
        <view
          class="emission-item"
          style="background-image: url('/static/images/index/waste.png')"
        >
          <text class="number">{{
            (alldata.saveCoal / 1000000).toFixed(2)
          }}</text>
          <text class="unit">千吨</text>
        </view>
      </view>
      <!-- </view> -->
    </TitleCardBox>
  </view>
</template>
<script>
import Area from "@/components/station/area.vue";
import TitleCardBox from "@/components/TitleCardBox/TitleCardBox.vue";
import CardBox from "@/components/CardBox/CardBox.vue";
import BeautifulProgress from '@/components/common/BeautifulProgress.vue';
import {
  getAllStationDaily,
  getAllStationData,
  getStationCurveData,
} from "@/api/station/station.js";
export default {
  components: {
    Area,
    TitleCardBox,
    CardBox,
    BeautifulProgress,
  },
  data() {
    return {
      sliderData: [
        {
          dayOutput: 0,
          monthOutput: 300000,
          yearOutput: 3600000,
          totalOutput: 50000000,
          image: "/static/images/index/station.png",
        },
        {
          dcapYield: 200,
          mcapYield: 400000,
          ycapYield: 4600000,
          tcapYield: 60000000,
          image: "/static/images/index/temp5.png",
        },
        // 可以添加更多数据项
      ],
      currentIndex: 0,
      toFixed: "",
      chartData: {
        categories: [], // x 轴数据
        series: [], // 数据系列
      },
      stationList: [
        {
          shortName: "",
          image: "/static/images/companyA.png",
          progress: 75,
          time: 5.2,
        },
        {
          companyName: "公司B",
          image: "/static/images/companyB.png",
          progress: 60,
          time: 4.7,
        },
        {
          companyName: "公司C",
          image: "/static/images/companyC.png",
          progress: 45,
          time: 3.9,
        },
      ],
      ringOpts: {
        timing: "easeOut",
        duration: 1000,
        rotate: false,
        rotateLock: false,
        color: [
          "#3fc8ae",
          "#ee8882",
          "#cccad5",
          "#6ba0e9",
          "#73C0DE",
          "#3CA272",
          "#FC8452",
          "#9A60B4",
          "#ea7ccc",
        ],
        // padding: [5, 5, 5, 5],
        fontSize: 13,
        fontColor: "#666666",
        dataLabel: false,
        dataPointShape: false,
        dataPointShapeType: "hollow",
        touchMoveLimit: 60,
        enableScroll: false,
        enableMarkLine: false,
        legend: {
          show: false,
          position: "bottom",
          lineHeight: 25,
          float: "bottom",
          padding: 5,
          margin: 5,
          backgroundColor: "rgba(0,0,0,0)",
          borderColor: "rgba(0,0,0,0)",
          borderWidth: 0,
          fontSize: 13,
          fontColor: "#666666",
          hiddenColor: "#CECECE",
          itemGap: 10,
        },
        title: {
          name: "",
          fontSize: 13,
          color: "#666666",
          offsetX: 0,
          offsetY: 0,
        },
        subtitle: {
          name: "12",
          fontSize: 25,
          color: "#000000",
          offsetX: 0,
          offsetY: 0,
        },
        extra: {
          ring: {
            ringWidth: 20,
            activeOpacity: 0.3,
            activeRadius: 10,
            offsetAngle: -90,
            labelWidth: 15,
            border: true,
            borderWidth: 2,
            borderColor: "#FFFFFF",
            centerColor: "#ffffff",
            customRadius: 0,
            linearType: "custom",
            customColor: ["#23aa91", "#d85863", "#aeadb5", "#487bee"],
          },
          tooltip: {
            showBox: true,
            showArrow: true,
            showCategory: false,
            borderWidth: 0,
            borderRadius: 0,
            borderColor: "#000000",
            borderOpacity: 0.7,
            bgColor: "#000000",
            bgOpacity: 0.7,
            gridType: "solid",
            dashLength: 4,
            gridColor: "#CCCCCC",
            boxPadding: 3,
            fontSize: 13,
            lineHeight: 20,
            fontColor: "#FFFFFF",
            legendShow: true,
            legendShape: "auto",
            splitLine: true,
            horizentalLine: false,
            xAxisLabel: false,
            yAxisLabel: false,
            labelBgColor: "#FFFFFF",
            labelBgOpacity: 0.7,
            labelFontColor: "#666666",
          },
        },
      },
      // 区域图
      parentCategories: ["6:00", 1, 1, 1, 1],
      parentSeries: [
        {
          name: "PV",
          data: [10, 0, 40, 20, 30],
          legendShape: "circle",
        },
      ],
      // 发电趋势分组
      dateSelectList: ["日", "月", "年", "总"],
      dateCurrent: 0,
      currentDate: new Date(), // 当前日期
      alldata: "",
      trendData: {
        output: [],
        xaxis: [],
        timeType: "day",
        unit: "kW",
        unitName: "功率",
      },
      queryParams: {
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
        day: new Date().getDate(),
      },
    };
  },
  onReady() {
    this.initData();
  },
  created() {
    this.getAllStationDaily();
  },
  computed: {
    formattedDate() {
      const date = this.currentDate;
      switch (this.dateCurrent) {
        case 0: // 日
          return date.toISOString().split("T")[0]; // YYYY-MM-DD
        case 1: // 月
          return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
            2,
            "0"
          )}`; // YYYY-MM
        case 2: // 年
          return `${date.getFullYear()}`; // YYYY
        default:
          return "";
      }
    },
    showDateControls() {
      return this.dateCurrent !== 3; // 如果不是"总"视图就显示日期控制
    },
    canGoForward() {
      if (this.dateCurrent === 3) return false;

      const today = new Date();
      const current = this.currentDate;

      switch (this.dateCurrent) {
        case 0: // 日视图
          return current.getTime() < today.setHours(0, 0, 0, 0);
        case 1: // 月视图
          return (
            current.getFullYear() < today.getFullYear() ||
            (current.getFullYear() === today.getFullYear() &&
              current.getMonth() < today.getMonth())
          );
        case 2: // 年视图
          return current.getFullYear() < today.getFullYear();
        default:
          return false;
      }
    },
  },

  methods: {
    formatValue(value) {
      if (value === undefined || value === null) return "0.00";
      return (value / 10000).toFixed(2);
    },
    onSlideChange(event) {
      this.currentIndex = event.detail.current;
    },
    goToList(status) {
      // Map 'connecting' from homepage to 'notConnected' in station page
      const statusMap = {
        'connecting': 'notConnected', // Map connecting to notConnected
        'normal': 'normal',
        'abnormal': 'abnormal',
        'offline': 'offline'
      };

      // Use the mapped status or the original status if no mapping exists
      const finalStatus = statusMap[status] || status;

      // 保存状态到全局变量或 Vuex
      uni.setStorageSync("tabStatus", finalStatus);
      uni.switchTab({
        url: "/pages/station/index",
      });
    },
    getServerData(data) {
      if (!data) return;

      setTimeout(() => {
        const seriesData = [
          {
            name: "正常",
            value: Number(data.normal) || 0,
          },
          {
            name: "异常",
            value: Number(data.abnormal) || 0,
          },
          {
            name: "离线",
            value: Number(data.offline) || 0,
          },
          {
            name: "连接中",
            value: Number(data.connecting) || 0,
          },
        ];

        this.chartData = {
          series: [
            {
              data: seriesData,
            },
          ],
        };

        const total = seriesData.reduce((sum, item) => sum + item.value, 0);
        this.ringOpts.subtitle.name = total.toString();
      }, 500);
    },
    dateChange(index) {
      this.dateCurrent = index;
      const today = new Date(); // 获取当前日期

      // 重置 currentDate 为今天的日期
      this.currentDate = new Date(today);

      // 根据不同的 tab 设置查询参数
      switch (index) {
        case 0: // 日视图 - 获取当天数据
          this.queryParams = {
            year: today.getFullYear(),
            month: today.getMonth() + 1,
            day: today.getDate(),
          };
          break;
        case 1: // 月视图 - 获取当月数据
          this.queryParams = {
            year: today.getFullYear(),
            month: today.getMonth() + 1,
          };
          break;
        case 2: // 年视图 - 获取当年数据
          this.queryParams = {
            year: today.getFullYear(),
          };
          break;
        case 3: // 总视图
          this.queryParams = {};
          break;
      }

      this.getTrendData();
    },
    changeDate(direction) {
      const date = new Date(this.currentDate);
      const today = new Date();

      switch (this.dateCurrent) {
        case 0: // 日视图
          date.setDate(date.getDate() + direction);
          // 限制不能超过今天
          if (date > today) {
            date.setTime(today.getTime());
          }
          break;
        case 1: // 月视图
          date.setMonth(date.getMonth() + direction);
          // 限制不能超过当前月
          if (
            date.getFullYear() > today.getFullYear() ||
            (date.getFullYear() === today.getFullYear() &&
              date.getMonth() > today.getMonth())
          ) {
            date.setFullYear(today.getFullYear());
            date.setMonth(today.getMonth());
          }
          break;
        case 2: // 年视图
          date.setFullYear(date.getFullYear() + direction);
          // 限制不能超过当前年
          if (date.getFullYear() > today.getFullYear()) {
            date.setFullYear(today.getFullYear());
          }
          break;
      }

      this.currentDate = date;

      // 更新查询参数
      switch (this.dateCurrent) {
        case 0: // 日视图
          this.queryParams = {
            year: date.getFullYear(),
            month: date.getMonth() + 1,
            day: date.getDate(),
          };
          break;
        case 1: // 月视图
          this.queryParams = {
            year: date.getFullYear(),
            month: date.getMonth() + 1,
          };
          break;
        case 2: // 年视图
          this.queryParams = {
            year: date.getFullYear(),
          };
          break;
      }

      this.getTrendData();
    },
    convertValue(value) {
      if (value === undefined || value === null) return "0";
      if (value >= 1000000) {
        return (value / 1000000).toFixed(2);
      } else if (value >= 1000) {
        return (value / 1000).toFixed(2);
      } else {
        return value.toFixed(2);
      }
    },
    async initData() {
      try {
        await this.getAllStationDaily();
        await this.getAllStationData();
        await this.getTrendData();
      } catch (error) {
        uni.showToast({
          title: "加载数据失败",
          icon: "none",
        });
      }
    },
    getAllStationDaily() {
      return getAllStationDaily().then((res) => {
        this.stationList = res.rows;
        return res.rows;
      });
    },
    // 显示完整公司名称
    showFullName(name) {
      uni.showToast({
        title: name,
        icon: 'none',
        duration: 2000
      });
    },
    getAllStationData() {
      return getAllStationData().then((res) => {
        this.alldata = res.data;
        this.getServerData(this.alldata);
        this.sliderData = [
          {
            dayOutput: this.alldata.dayOutput,
            monthOutput: this.alldata.monthOutput,
            yearOutput: this.alldata.yearOutput,
            totalOutput: this.alldata.totalOutput,
            image: "/static/images/index/station.png",
          },
          {
            dcapYield: this.alldata.dcapYield,
            mcapYield: this.alldata.mcapYield,
            ycapYield: this.alldata.ycapYield,
            tcapYield: this.alldata.tcapYield,
            image: "/static/images/index/station.png",
          },
        ];
        return res.data;
      });
    },
    goToMore() {
      uni.navigateTo({
        url: "/pages/common/rankview/index",
      });
    },
    calculateProgress(equivalentTime) {
      // 最大值为 8 小时，进度条的最大值为 100%
      const maxTime = 8;
      const progress = (equivalentTime / maxTime) * 100;
      return Math.min(progress, 100); // 确保不超过 100%
    },
    fetchData() {
      const params = {
        stationId: 11,
        year: 2024,
        month: 10,
        day: 28,
      };

      getStationCurveData(params).then((response) => {
        const rows = response.rows || [];
        if (rows.length === 0) {
          this.parentCategories = [];
          this.parentSeries = [
            {
              name: "PV",
              data: [],
              legendShape: "circle",
            },
          ];
          return;
        }
        // 更新 categories
        this.parentCategories = rows.map((item) => {
          const hours = Math.floor(item.hm / 100);
          const minutes = item.hm % 100;
          return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(
            2,
            "0"
          )}`;
        });
        // 更新 parentSeries
        this.parentSeries = [
          {
            name: "PV",
            data: rows.map((item) => item.output || 0), // 使用 0 代替 undefined
            legendShape: "circle",
          },
        ];
      });
    },

    async getTrendData() {
      try {
        const res = await getStationCurveData(this.queryParams);
        if (res.code === 200 && res.data) {
          let outputData = [];
          let unit = "";
          let unitName = "";

          // 根据视图类型选择相应的数据
          switch (this.dateCurrent) {
            case 0: // 日视图
              outputData = res.data.acPower || [];
              unit = "kW";
              unitName = "功率";
              if (Math.max(...outputData) > 1000) {
                //将数值转化为kw基础单位
                outputData = outputData.map((val) =>
                  Number((val / 1000).toFixed(2))
                );
                unit = "kW"; // 日视图的单位是 kW
              } else {
                unit = "W"; // 日视图的单位是 W
              }
              break;
            case 1: // 月视图
            case 2: // 年视图
            case 3: // 总视图
              outputData = res.data.output || [];
              unit = "度";
              unitName = "发电量";
              // 如果数值较大，转换为万度
              if (Math.max(...outputData) > 10000) {
                outputData = outputData.map((val) =>
                  Number((val / 10000).toFixed(2))
                );
                unit = "万度"; // 万度单位
              } else {
                unit = "度"; // KWh单位
              }
              break;
            default:
              outputData = [];
              unit = "";
              unitName = "";
          }

          this.trendData = {
            output: outputData,
            xaxis: res.data.xaxis || [],
            timeType: ["day", "month", "year", "total"][this.dateCurrent],
            unit,
            unitName,
          };

          // 确保 outputData 是有效的数组，并对所有数据进行除以1000操作
          if (Array.isArray(outputData) && outputData.length > 0) {
            outputData = outputData.map((value) => value / 1000);
          } else {
            ("outputData is not an array or is empty");
          }

          // 更新 parentSeries
          this.parentSeries = [
            {
              name: "功率",
              data: this.trendData.output,
              legendShape: "circle",
            },
          ];
          // 根据视图类型选择相应的数据
          switch (this.dateCurrent) {
            case 0: // 日视图
              // 更新 parentSeries
              this.parentSeries = [
                {
                  name: "功率",
                  data: this.trendData.output,
                  legendShape: "circle",
                },
              ];
              break;
            case 1: // 月视图
            case 2: // 年视图
            case 3: // 总视图
              // 更新 parentSeries
              this.parentSeries = [
                {
                  name: "发电量",
                  data: this.trendData.output,
                  legendShape: "circle",
                },
              ];
              break;
            default:
              // 更新 parentSeries
              this.parentSeries = [
                {
                  name: "功率",
                  data: this.trendData.output,
                  legendShape: "circle",
                },
              ];
          }

          // 更新图表数据
          this.parentCategories = this.trendData.xaxis;
        } else {
          uni.showToast({
            title: res.msg || "获取数据失败",
            icon: "none",
          });
        }
      } catch (error) {
        console.error("获取发电趋势数据失败:", error);
        uni.showToast({
          title: "获取数据失败",
          icon: "none",
        });
      }
    },
    getPowerUnit(power) {
      if (!power) return "";
      if (power >= 1000000) {
        return "(MW)";
      } else if (power >= 1000) {
        return "(kW)";
      } else {
        return "(W)";
      }
    },
    formatPowerValue(power) {
      if (!power) return "0";
      if (power >= 1000000) {
        return (power / 1000000).toFixed(2);
      } else if (power >= 1000) {
        return (power / 1000).toFixed(2);
      } else {
        return power.toFixed(2);
      }
    },
  },
};
</script>
<style lang="scss">
page {
  background-color: #f5f6f7;
}

.indicator-container {
  display: flex;
  justify-content: center;
  margin-top: 10rpx;
  /* 指示器与 swiper 之间的间距 */
}

.indicator-line {
  display: flex;
  position: relative;
  width: 10%;
  /* 根据需要调整 */
  height: 5rpx;
  /* 线条的高度 */
  background-color: lightgray;
  /* 默认线条颜色 */
  border-radius: 2.5rpx;
  /* 圆角效果 */
}

.indicator {
  flex: 1;
  /* 使每个指示器等分 */
  height: 100%;
  transition: background-color 0.3s;
  /* 过渡效果 */
}

.indicator.active {
  background-color: #3fc8ae;
  /* 激活状态颜色 */
}

.main-content {
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  padding-top: 40rpx;

  .part-one {
    // 第一部分整体样式
    display: flex;
    flex-direction: column;

    .part-one-top {
      // 第一部分上半部分样式
      width: 100%;
      height: 65%;
      display: flex;
      align-items: flex-end;
      justify-content: space-between;

      .data-display {
        // 数据展示区域样式
        display: flex;
        flex-direction: column;
        margin-bottom: 0rpx;

        .data-title-bold {
          // 当日发电标题样式
          // font-weight: bold;
          font-size: 28rpx;
          margin-bottom: 10rpx;
        }

        .data-num-bold {
          // 当日发电数字样式
          font-weight: 600;
          font-size: 70rpx;
        }
      }

      .bg-image {
        // 背景图片区域样式
        img {
          width: 160rpx;
          height: auto;
        }
      }
    }

    .bg-image img.zoom {
      transform: scale(0.8);
      /* 放大到 1.2 倍 */
    }

    .part-one-bottom {
      margin-top: 20rpx;
      height: auto;

      // 第一部分底部样式
      .detailed-indicators {
        // 详细指标区域样式
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .top-item-box {
          // 每个指标盒子样式
          display: flex;
          flex-direction: column;

          .item-title {
            // 指标标题样式
            font-size: 26rpx;
            color: #666;
          }

          .item-number {
            // 指标数字样式
            font-size: 30rpx;
            font-weight: bold;
            margin-top: 14rpx;
          }
        }

        .line {
          height: 60rpx;
          width: 2rpx;
          background-color: #ededed;
        }
      }
    }
  }

  .part-two {
    // 第二部分整体样式
    display: flex;
    flex-direction: column;
    margin-top: 40rpx;

    .chart-area {
      // 图表区域样式
      display: flex;
      justify-content: center;
      align-items: center;

      .chart-left {
        width: 50%;

        // 左图表样式
      }

      .chart-right {
        display: flex;
        height: 100%;

        .ring-num {
          display: flex;
          flex-direction: column;

          .online,
          .abnormal,
          .offline,
          .unconnected {
            display: flex;
            align-items: center;

            .sign {
              height: 20rpx;
              border-radius: 5rpx;
              width: 8rpx;
              background: linear-gradient(to bottom, #3fc8ae, #23aa91);
            }

            .status-text {
              width: 3.5em;
              margin-left: 20rpx;
              font-size: 28rpx;
            }

            .station-status-num {
              font-size: 30rpx;
              font-weight: bold;
              margin-right: 10rpx;
            }
          }

          .abnormal,
          .offline,
          .unconnected {
            margin-top: 10rpx;
          }

          .abnormal {
            .sign {
              background: linear-gradient(to bottom, #ee8882, #d85863);
            }
          }

          .offline {
            .sign {
              background: linear-gradient(to bottom, #cccad5, #aeadb5);
            }
          }

          .unconnected {
            .sign {
              background: linear-gradient(to bottom, #6ba0e9, #487bee);
            }
          }
        }
      }
    }

    .dividing-line {
      width: 100%;
      height: 2rpx;
      background-color: #ededed;
      margin-top: 10rpx;
      margin-bottom: 26rpx;
    }

    .detailed-data {
      // 详细指标区域样式
      width: 100%;
      display: flex;
      justify-content: space-around;
      align-items: flex-start;

      .top-item-box {
        // 每个指标盒子样式
        display: flex;
        justify-content: flex-start;
        flex-direction: column;
        width: 30%;

        .item-title {
          // 指标标题样式
          font-size: 26rpx;
          color: #666;
        }

        .item-number {
          // 指标数字样式
          font-size: 30rpx;
          font-weight: bold;
          margin-top: 14rpx;
        }
      }

      .line {
        height: 60rpx;
        width: 2rpx;
        background-color: #ededed;
      }
    }
  }

  .part-three {
    display: flex;
    flex-direction: column;

    .power-trend-card {
      display: flex;
      flex-direction: column;

      .trend-date-select {
        margin-bottom: 50rpx;

        .data-subsection {
          display: flex;
          justify-content: center;
        }

        .date-num {
          margin-top: 40rpx;
          display: flex;
          justify-content: center;
          align-items: center;

          .date-val {
            font-size: 24rpx;
            margin: 0 40rpx;
          }
        }
      }
    }
  }

  /* 发电趋势日期选择器样式 */
  .trend-date-select {
    margin-bottom: 30rpx;

    .data-subsection {
      margin-bottom: 20rpx;
      /* 日期选择器样式 */
    }

    .date-num {
      margin-top: 20rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      .date-val {
        font-size: 24rpx;
        margin: 0 40rpx;
      }
    }
  }
}

.rankdemo {
  font-size: 17px;
  margin-top: 18rpx;
}

.company-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  display: block;
}

.ranking-box {
  background-color: #ffffff;
  padding: 24rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
  margin-bottom: 20rpx;
  border: 1rpx solid #f0f0f0;
}

.ranking-list {
  margin-top: 0px;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  height: 120rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-content {
  flex: 1;
  margin-left: 10rpx;
  padding: 0 10rpx;
  width: 0; /* 确保内容区域可以收缩 */
}

.progress-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
}

.progress-dot {
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  background-color: #4cd964;
  margin-right: 8rpx;
  flex-shrink: 0;
}

.u-line-progress {
  height: 16rpx !important;
  border-radius: 8rpx !important;
  overflow: hidden;
  background: #e9f5eb !important;
  flex: 1;
}

.u-line-progress .u-line-progress-bg {
  background-color: #e9f5eb !important;
  border-radius: 8rpx !important;
}

.u-line-progress .u-line-progress-fill {
  background-color: #4cd964 !important;
  border-radius: 8rpx !important;
}

.time {
  font-size: 28rpx;
  color: #4cd964;
  font-weight: 600;
  width: 80rpx;
  text-align: right;
  margin-left: 16rpx;
  flex-shrink: 0;
}

.device-image {
  width: 80rpx;
  height: 60rpx;
  border-radius: 6rpx;
  object-fit: contain;
  margin: 0 16rpx;
  background-color: #f8f8f8;
  flex-shrink: 0;
}

.title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.ranking-title-left {
  display: flex;
  align-items: center;
}

.title {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
}

.ranking-title-right {
  display: flex;
  align-items: center;
}

.more-text {
  font-size: 26rpx;
  color: #999;
}

.rank {
  font-size: 32rpx;
  font-weight: bold;
  color: #666;
  min-width: 50rpx;
  text-align: center;
}

/* 前三名特殊样式 */
.ranking-item:nth-child(1) .rank {
  color: #f0a54a;
}

.ranking-item:nth-child(2) .rank {
  color: #a0a0a0;
}

.ranking-item:nth-child(3) .rank {
  color: #c87f32;
}

.emission-box {
  background-color: #ffffff;
  border-radius: 18rpx;
  padding: 26rpx;
  margin-top: 20rpx;
}

.title {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
  /* 增加底部间距 */
  text-align: left;
  /* 使标题居中 */
}

.emission-container {
  display: flex;
  flex-wrap: wrap;
  /* 允许换行 */
  justify-content: space-around;
  /* 均匀分布项 */
}

.emission-item {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 30%;
  /* 设置每个项的宽度为30% */
  height: 180rpx;
  /* 设置高度 */
  background-size: cover;
  /* 背景图像覆盖 */
  background-position: center;
  /* 背景图像居中 */
  margin-bottom: 5rpx;
  /* 增加底部间距 */
  color: #fff;
  /* 字体颜色 */
  font-size: 24rpx;
  /* 字体大小 */
  text-align: center;
  /* 使文本居中 */
  border-radius: 8rpx;
  /* 圆角 */
}

.number {
  font-size: 32rpx;
  /* 数字放大 */
  font-weight: bold;
  /* 加粗 */
  margin-right: 10rpx;
  /* 增加右侧间距 */
  color: #333;
  margin-bottom: 50rpx;
}

.unit {
  font-size: 24rpx;
  /* 单位大小不变 */
  color: #333;
  /* 单位颜色 */
  margin-bottom: 50rpx;
  margin-left: 0;
  /* 去掉左侧间距 */
}
</style>