<template>
	<view>
		<view class="container">
			<view class="title">{{ title }}</view>
			<!-- 添加插槽，用于嵌套其他组件 -->
			<slot></slot>
		</view>
	</view>
</template>

<script>
	export default {
		name: "TitleCardBox",
		props: {
			title: {
				type: String,
				default: '',
			},
		},
		data() {
			return {};
		},
	};
</script>

<style lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		background-color: #ffffff;
		padding: 36rpx 30rpx;
		border-radius: 20rpx;
		margin-bottom: 20rpx;
		// margin-top: 20rpx;

		.title {
			font-size: 30rpx;
			font-weight: bold;
			margin-bottom: 20rpx;
		}
	}
</style>