<template>
    <view class="login-container">
        <!-- 顶部图片区域 -->
        <view class="top-image">
            <image src="../static/images/login/login-bg.jpg" mode="aspectFill" class="illustration"></image>
        </view>

        <!-- 注册框 -->
        <view class="login-box">
            <!-- Logo和标题 -->
            <view class="header">
                <text class="title">账号注册</text>
            </view>

            <!-- 注册表单 -->
            <view class="login-form">
                <!-- 用户名输入框 -->
                <view class="input-group">
                    <text class="input-label">账号</text>
                    <view class="input-with-icon">
                        <view class="iconfont icon-user icon"></view>
                        <input 
                            v-model="registerForm.username" 
                            class="input" 
                            type="text" 
                            placeholder="请输入账号" 
                            maxlength="30"
                            placeholder-class="placeholder"
                        />
                    </view>
                </view>

                <!-- 密码输入框 -->
                <view class="input-group">
                    <text class="input-label">密码</text>
                    <view class="input-with-icon">
                        <view class="iconfont icon-password icon"></view>
                        <input 
                            v-model="registerForm.password" 
                            type="password" 
                            class="input" 
                            placeholder="请输入密码" 
                            maxlength="20"
                            placeholder-class="placeholder"
                        />
                    </view>
                </view>

                <!-- 确认密码输入框 -->
                <view class="input-group">
                    <text class="input-label">确认密码</text>
                    <view class="input-with-icon">
                        <view class="iconfont icon-password icon"></view>
                        <input 
                            v-model="registerForm.confirmPassword" 
                            type="password" 
                            class="input" 
                            placeholder="请再次输入密码" 
                            maxlength="20"
                            placeholder-class="placeholder"
                        />
                    </view>
                </view>

                <!-- 验证码 -->
                <view class="input-group verification" v-if="captchaEnabled">
                    <text class="input-label">验证码</text>
                    <view class="verification-box">
                        <view class="input-with-icon captcha-input-box">
                            <view class="iconfont icon-code icon"></view>
                            <input 
                                v-model="registerForm.code" 
                                type="number" 
                                class="input" 
                                placeholder="请输入验证码" 
                                maxlength="4"
                                placeholder-class="placeholder"
                            />
                        </view>
                        <image :src="codeUrl" @click="getCode" class="captcha-image"></image>
                    </view>
                </view>

                <!-- 注册按钮 -->
                <button class="login-btn" @click="handleRegister">
                    注 册
                </button>

                <!-- 底部链接 -->
                <view class="bottom-links">
                    <text class="link-text" @tap="handleUserLogin">使用已有账号登录</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    import {getCodeImg, register} from '@/api/login'

    export default {
        data() {
            return {
                codeUrl: "",
                captchaEnabled: true,
                globalConfig: getApp().globalData.config,
                registerForm: {
                    username: "",
                    password: "",
                    confirmPassword: "",
                    code: "",
                    uuid: ''
                }
            }
        },
        created() {
            this.getCode()
        },
        methods: {
            // 用户登录
            handleUserLogin() {
                this.$tab.navigateTo(`/pages/login`)
            },
            // 获取图形验证码
            getCode() {
                getCodeImg().then(res => {
                    this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled
                    if (this.captchaEnabled) {
                        this.codeUrl = 'data:image/gif;base64,' + res.img
                        this.registerForm.uuid = res.uuid
                    }
                })
            },
            // 注册方法
            async handleRegister() {
                if (this.registerForm.username === "") {
                    this.$modal.msgError("请输入您的账号")
                } else if (this.registerForm.password === "") {
                    this.$modal.msgError("请输入您的密码")
                } else if (this.registerForm.confirmPassword === "") {
                    this.$modal.msgError("请再次输入您的密码")
                } else if (this.registerForm.password !== this.registerForm.confirmPassword) {
                    this.$modal.msgError("两次输入的密码不一致")
                } else if (this.registerForm.code === "" && this.captchaEnabled) {
                    this.$modal.msgError("请输入验证码")
                } else {
                    this.$modal.loading("注册中，请耐心等待...")
                    this.register()
                }
            },
            // 用户注册
            async register() {
                register(this.registerForm).then(res => {
                    this.$modal.closeLoading()
                    uni.showModal({
                        title: "系统提示",
                        content: "恭喜你，您的账号 " + this.registerForm.username + " 注册成功！",
                        success: function (res) {
                            if (res.confirm) {
                                uni.redirectTo({url: `/pages/login`});
                            }
                        }
                    })
                }).catch(() => {
                    if (this.captchaEnabled) {
                        this.getCode()
                    }
                })
            },
            // 注册成功后，处理函数
            registerSuccess(result) {
                // 设置用户信息
                this.$store.dispatch('GetInfo').then(res => {
                    this.$tab.reLaunch('/pages/index')
                })
            }
        }
    }
</script>

<style lang="scss">
.login-container {
    min-height: 100vh;
    background: #fff;
    display: flex;
    flex-direction: column;
}

.top-image {
    height: 400rpx;
    overflow: hidden;
    
    .illustration {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}

.login-box {
    flex: 1;
    margin-top: -50rpx;
    background: #fff;
    border-radius: 40rpx 40rpx 0 0;
    padding: 50rpx 40rpx;
    position: relative;
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.header {
    text-align: center;
    margin-bottom: 60rpx;
    
    .title {
        font-size: 36rpx;
        color: #333;
        font-weight: 600;
    }
}

.login-form {
    .input-group {
        margin-bottom: 30rpx;
        
        .input-label {
            font-size: 28rpx;
            color: #666;
            margin-bottom: 16rpx;
            display: block;
        }
        
        .input-with-icon {
            display: flex;
            align-items: center;
            background: #f5f7fa;
            border-radius: 45rpx;
            padding: 0 40rpx;
            
            .icon {
                font-size: 38rpx;
                color: #999;
                margin-right: 20rpx;
            }
            
            .input {
                flex: 1;
                height: 90rpx;
                font-size: 30rpx;
                color: #333;
                background: transparent;
            }
        }
        
        &.verification {
            .verification-box {
                display: flex;
                align-items: center;
                gap: 20rpx;
                
                .captcha-input-box {
                    flex: 1;
                }
                
                .captcha-image {
                    width: 200rpx;
                    height: 90rpx;
                    border-radius: 45rpx;
                }
            }
        }
    }
}

.login-btn {
    width: 100%;
    height: 90rpx;
    line-height: 90rpx;
    background: #4CAF50;
    color: #fff;
    font-size: 32rpx;
    font-weight: 600;
    border-radius: 45rpx;
    margin-top: 60rpx;
    border: none;
    transition: all 0.3s;
    
    &:active {
        transform: scale(0.98);
        opacity: 0.9;
    }
}

.bottom-links {
    display: flex;
    justify-content: center;
    margin-top: 40rpx;
    padding: 0 20rpx;
    
    .link-text {
        font-size: 28rpx;
        color: #666;
        
        &:active {
            opacity: 0.7;
        }
    }
}

.placeholder {
    color: #999;
    font-size: 28rpx;
}

/* H5适配 */
// #ifdef H5
.login-container {
    height: 100vh;
}

.login-box {
    overflow-y: auto;
}
// #endif
</style>
