<template>
	<view class="container">
		<view class="tab-container">
			<view class="tab" :class="{ active: activeTab === 'history' }" @click="changeTab('history')">
				未关闭
			</view>
			<view class="tab" :class="{ active: activeTab === 'unclosed' }" @click="changeTab('unclosed')">
				历史故障
			</view>
			<view class="tab-line" :style="{ left: activeTab === 'history' ? '0' : '50%' }"></view>
		</view>

		<view class="icon-container">
			<view class="checkbox-wrapper">
				<u-checkbox-group v-model="checkboxValues" placement="row" @change="groupChange">
					<view class="icon-item" v-for="(item, index) in checkboxList" :key="index">
						<u-checkbox :label="item.name" :name="item.name" shape="circle">
							<template #label>
								<view class="checkbox-content">
									<text class="icon-text">{{ item.name }}</text>
								</view>
							</template>
						</u-checkbox>
					</view>
				</u-checkbox-group>
			</view>
		</view>

		<view style="padding: 20rpx">
			<!-- 未关闭故障卡片 -->
			<CardBox v-if="activeTab === 'unclosed' && unclosedFaults.length" v-for="(fault, index) in unclosedFaults"
				:key="'unclosed-' + fault.id + '-' + index" @click="goToFaultDetail(fault.id)">
				<view style="padding: 10rpx">
					<view class="fault-header">
						<view class="small-button">故障</view>
						<text class="fault-text">{{ fault.faultName }}</text>
						<text class="closed-text">{{ fault.processStatusStr }}</text>
					</view>
					<view class="action-container">
						<view class="tric-demo">
							<image src="/static/images/index/tric.png" mode="aspectFit" class="small-icon" />
						</view>
						<text class="action-text">{{ fault.stationFullName }}</text>
					</view>
					<view class="action-container">
						<view class="pd-demo">
							<image src="/static/images/index/pd.png" mode="aspectFit" class="small-icon" />
						</view>
						<text class="action-text">{{ fault.eqName }}</text>
					</view>
					<view class="divider"></view>
					<view class="time-container">
						<text class="time-text">{{ fault.processTime }}</text>
					</view>
				</view>
			</CardBox>

			<!-- 历史故障卡片 -->
			<CardBox v-if="activeTab === 'history' && historyFaults.length" v-for="(fault, index) in historyFaults"
				:key="'history-' + fault.id + '-' + index" @click="goToFaultDetail(fault.id)">
				<view style="padding: 10rpx">
					<view class="fault-header">
						<view class="small-button">故障</view>
						<text class="fault-text">{{ fault.faultName }}</text>
						<text class="closed-text">{{ fault.processStatusStr }}</text>
					</view>
					<view class="action-container">
						<view class="tric-demo">
							<image src="/static/images/index/tric.png" mode="aspectFit" class="small-icon" />
						</view>
						<text class="action-text">{{ fault.stationFullName }}</text>
					</view>
					<view class="action-container">
						<view class="pd-demo">
							<image src="/static/images/index/pd.png" mode="aspectFit" class="small-icon" />
						</view>
						<text class="action-text">{{ fault.eqName }}</text>
					</view>
					<view class="divider"></view>
					<view class="time-container">
						<text class="time-text">{{
              parseTime(fault.processTime, "{y}-{m}-{d} {h}:{i}:{s}")
            }}</text>
					</view>
				</view>
			</CardBox>
		</view>

		<!-- 修改底部加载状态显示 -->
		<view class="loading-status" v-if="activeTab === 'history' || activeTab === 'unclosed'">
			<view v-if="loading" class="loading-wrapper">
				<u-loading-icon size="28"></u-loading-icon>
				<text class="loading-text">正在加载中...</text>
			</view>
			<text v-else-if="
          currentPage * pageSize >=
          (activeTab === 'unclosed'
            ? allUnclosedFaults.length
            : allHistoryFaults.length)
        " class="no-more-text">
				没有更多数据了
			</text>
		</view>
	</view>
</template>

<script>
	import CardBox from "@/components/CardBox/CardBox.vue";
	import {
		getEquipmentWarning
	} from "@/api/station/station.js";

	export default {
		data() {
			return {
				activeTab: "history", // 修改默认激活的标签为 history
				unclosedFaults: [], // 存储未关闭的故障
				historyFaults: [], // 存储历史故障
				checkboxValues: ["故障", "告警"], // 默认选中故障和告警
				checkboxList: [{
						name: "故障"
					},
					{
						name: "告警"
					},
					{
						name: "提示"
					},
					{
						name: "建议"
					},
				],
				stationId: null,
				pageSize: 10, // 每页显示数量
				currentPage: 1, // 当前页码
				allUnclosedFaults: [], // 存储所有未关闭故障数据
				allHistoryFaults: [], // 存储所有历史故障数据
				loading: false, // 是否正在加载
				isRefreshing: false, // 添加下拉刷新状态
			};
		},
		onLoad(options) {
			this.stationId = options.id;
			("当前 stationId:", this.stationId);

			// 默认加载未关闭故障数据，并清空复选框数据
			this.changeTab("history");
		},
		onPullDownRefresh() {
			this.isRefreshing = true;
			// 重新加载当前标签页的数据
			this.currentPage = 1;
			this.getEquipmentWarning(this.activeTab).then(() => {
				this.isRefreshing = false;
				uni.stopPullDownRefresh();
			});
		},
		methods: {
			changeTab(tab) {
				this.activeTab = tab;
				this.currentPage = 1; // 重置页码

				if (tab === "history") {
					this.unclosedFaults = [];
					this.historyFaults = [];
					this.checkboxValues = ["故障", "告警"];
					this.getEquipmentWarning("history");
				} else if (tab === "unclosed") {
					this.unclosedFaults = [];
					this.historyFaults = [];
					this.getEquipmentWarning("unclosed");
				}
			},

			getEquipmentWarning(type = "history") {
				this.loading = true;
				return getEquipmentWarning(this.stationId) // 添加return以支持Promise链式调用
					.then((response) => {
						const formattedData = response.rows.map((item) => ({
							...item,
							processTime: this.parseTime(
								item.processTime,
								"{y}-{m}-{d} {h}:{i}:{s}"
							),
						}));

						// 根据故障状态分类数据
						const unclosedData = formattedData.filter(
							(item) => item.processStatus === "已关闭"
						);
						const historyData = formattedData.filter(
							(item) => item.processStatus !== "已关闭"
						);

						if (type === " history") {
							this.allHistoryFaults = unclosedData; // 修正：历史故障显示 processStatus === '已关闭' 的数据
							this.loadMoreData("history");
						} else if (type === "unclosed") {
							this.allUnclosedFaults = historyData; // 修正：未关闭显示 processStatus !== '已关闭' 的数据
							this.loadMoreData("unclosed");
						}
						this.loading = false;
					})
					.catch((error) => {
						console.error("获取故障数据失败:", error);
						this.loading = false;
					});
			},

			loadMoreData(type) {
				this.loading = true; // 开始加载时显示加载状态
				
				// 模拟网络请求延迟
				setTimeout(() => {
					const start = (this.currentPage - 1) * this.pageSize;
					const end = this.currentPage * this.pageSize;

					if (type === "unclosed") {
						const newData = this.allUnclosedFaults.slice(start, end);
						this.unclosedFaults = this.currentPage === 1 ? 
							newData : [...this.unclosedFaults, ...newData];
					} else if (type === "history") {
						const newData = this.allHistoryFaults.slice(start, end);
						this.historyFaults = this.currentPage === 1 ? 
							newData : [...this.historyFaults, ...newData];
					}
					
					this.loading = false; // 加载完成后隐藏加载状态
				}, 800); // 设置 800ms 的延迟以展示加载效果
			},

			goToFaultDetail(faultId) {
				("跳转到故障详情页面，传递的故障ID:", faultId);
				if (faultId) {
					uni.navigateTo({
						url: `/pages/warn/StationFaultPage?id=${faultId}`,
					});
				} else {
					console.error("故障ID无效");
				}
			},

			groupChange() {
				// 复选框组状态发生变化时触发的操作
				("复选框值变化:", this.checkboxValues);
			},

			checkboxChange(e) {
				// 每个复选框状态发生变化时的处理
				("单个复选框变化:", e);
			},

			parseTime(rawTime, format) {
				const date = new Date(rawTime);
				const formats = {
					"{y}": date.getFullYear(),
					"{m}": String(date.getMonth() + 1).padStart(2, "0"),
					"{d}": String(date.getDate()).padStart(2, "0"),
					"{h}": String(date.getHours()).padStart(2, "0"),
					"{i}": String(date.getMinutes()).padStart(2, "0"),
					"{s}": String(date.getSeconds()).padStart(2, "0"),
				};
				for (let key in formats) {
					format = format.replace(key, formats[key]);
				}
				return format;
			},

			// 添加滚动到底部的处理方法
			onReachBottom() {
				const currentData =
					this.activeTab === "unclosed" ?
					this.allUnclosedFaults :
					this.allHistoryFaults;

				const hasMore = this.currentPage * this.pageSize < currentData.length;

				if (hasMore && !this.loading) {
					this.currentPage++;
					this.loadMoreData(this.activeTab);
				}
			},
		},
	};
</script>

<style scoped>
	.container {
		display: flex;
		flex-direction: column;
		padding: 0rpx;
		background-color: #f5f5f5;
		width: 100%;
	}

	.tab-container {
		position: relative;
		display: flex;
		justify-content: center;
		background-color: #fff;
		flex-wrap: wrap;
		/* 支持换行 */
	}

	.tab {
		display: flex;
		flex: 1;
		justify-content: center;
		align-items: center;
		cursor: pointer;
		transition: color 0.3s;
		height: 60rpx;
		/* 增加高度 */
		min-width: 80rpx;
		/* 最小宽度以适应不同屏幕 */
	}

	.tab.active {
		color: rgb(58, 189, 58);
	}

	.tab-line {
		position: absolute;
		bottom: 0;
		height: 8rpx;
		/* 底线高度 */
		background-color: rgb(58, 189, 58);
		width: 10%;
		left: 0;
		transform: translateX(200%);
		transition: left 0.3s;
	}

	.icon-container {
		display: flex;
		justify-content: center;
		/* 居中对齐 */
		padding: 1rpx;
		/* 添加内边距 */
	}

	.checkbox-wrapper {
		background-color: #fff;
		/* 背景颜色 */
		padding: 20rpx;
		/* 内边距 */
		width: 100%;
		/* 使其占满父容器 */
	}

	.u-checkbox-group {
		display: flex;
		/* 使用 flex 布局 */
		justify-content: space-between;
		/* 均匀分布 */
		flex-wrap: wrap;
		/* 换行 */
	}

	.icon-item {
		flex: 1;
		/* 每个复选框占据相同空间 */
		min-width: 80rpx;
		/* 设置最小宽度 */
		max-width: 150rpx;
		/* 设置最大宽度 */
		margin: 1rpx;
		/* 增加间距 */
		display: flex;
		/* 使内容居中 */
		justify-content: center;
		/* 居中对齐 */
		align-items: center;
		/* 垂直居中 */
	}

	.icon-text {
		font-size: 26rpx;
		/* 字体大小 */
		color: #333;
	}

	.fault-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 10rpx;
		width: 100%;
	}

	.small-button {
		background-color: #f6f6f8;
		color: #c0bfca;
		padding: 0 10rpx;
		border-radius: 5rpx;
		margin-right: 10rpx;
		border: none;
		font-size: 16rpx;
		font-weight: bold;
	}

	.fault-text {
		font-size: 26rpx;
		color: #333;
		flex: 1;
		/* 让文本自适应 */
		text-align: left;
		/* 确保文本左对齐 */
		font-weight: bold;
	}

	.closed-text {
		font-size: 16rpx;
		color: #999;
		margin-left: 0;
	}

	.action-container {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		border-radius: 35rpx;
	}

	.small-icon {
		margin-left: 10rpx;
		width: 30rpx;
		height: 30rpx;
	}

	.action-text {
		font-size: 22rpx;
		color: #aaa;
		margin-left: 12rpx;
	}

	.divider {
		height: 1rpx;
		background-color: #ddd;
		margin: 12rpx 0;
	}

	.time-container {
		text-align: left;
		width: 100%;
		/* 确保容器占满宽度 */
	}

	.time-text {
		font-size: 25rpx;
		color: #999;
		flex: 1;
		/* 使文本自适应 */
		text-align: right;
		/* 右对齐 */
	}

	.loading-status {
		text-align: center;
		padding: 20rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.loading-wrapper {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 10rpx;
	}

	.loading-text {
		color: #999;
		font-size: 24rpx;
		margin-left: 10rpx;
	}

	.no-more-text {
		color: #999;
		font-size: 24rpx;
	}

	/* 添加下拉刷新的样式 */
	.refresh-indicator {
		text-align: center;
		padding: 20rpx;
		color: #999;
		font-size: 24rpx;
	}
</style>