<template>
  <view class="app-container">
    <u-navbar back-text="返回" :title="stationData.fullName" :placeholder="true" :titleStyle="{
      fontSize: '36rpx',
      fontWeight: 'bold',
    }" @leftClick="navLeftClick"></u-navbar>

    <view class="top-tabs">
      <u-tabs :list="tabsList" :is-scroll="false" :current="tabsCurrent" :scrollable="false" lineColor="#40b780"
        :activeStyle="{
          color: '#40b780',
          fontSize: '28rpx',
          fontWeight: 'bold',
          transform: 'scale(1.01)',
        }" :inactiveStyle="{
          color: '#606266',
          fontSize: '28rpx',
          transform: 'scale(1)',
        }" itemStyle="height: 90rpx;" @change="tabsChange"></u-tabs>
    </view>
    <view v-show="tabsCurrent === 0" class="gailan-box">
      <CardBox class="station-info-card">
        <view class="station-card-part1">
          <view class="weather-data">
            <uni-icons type="location" size="18" style="color: #4fbb9d"></uni-icons>
            {{ stationData.weather }} {{ stationData.temperature }}℃
          </view>
          <view class="station-status">
            <uni-icons type="checkbox-filled" size="18" style="color: #4fbb9d"></uni-icons>
            <text class="status-text">{{ stationData.status }}</text>
          </view>
        </view>
        <view class="station-card-part2">
          <view class="data-bg">
            <view class="param-title">
              当日发电{{ getDayUnit(stationData.dcap) }}
            </view>
            <view class="param-val">
              {{ formatDayValue(stationData.dcap) }}
            </view>
          </view>
          <view class="data-bg">
            <view class="param-title"> 当日收益(元) </view>
            <view class="param-val1">
              -----
            </view>
          </view>
        </view>
        <view class="station-card-part3">
          <view class="part3-box">
            <view class="station-data-box">
              <view class="">
                <view class="data-box-title">
                  当月发电{{ getDayUnit(stationData.mcap) }}
                </view>
                <view class="data-box-val">
                  {{ formatDayValue(stationData.mcap) }}
                </view>
              </view>
              <view class="">
                <view class="data-box-title"> 当月收益(元) </view>
                <view class="data-box-val">
                  -----
                </view>
              </view>
            </view>

            <view style="width: 1rpx; height: 85%; background-color: #e6e6e6"></view>
            <view class="station-data-box">
              <view class="">
                <view class="data-box-title"> 当年发电{{ getDayUnit(stationData.ycap) }} </view>
                <view class="data-box-val">
                  {{ ((stationData.ycap || 0) / 10000).toFixed(2) }}
                </view>
              </view>
              <view class="">
                <view class="data-box-title"> 当年收益(万元) </view>
                <view class="data-box-val">
                  -----
                </view>
              </view>
            </view>
            <view style="width: 1rpx; height: 85%; background-color: #e6e6e6"></view>
            <view class="station-data-box">
              <view class="">
                <view class="data-box-title"> 累计发电{{ getDayUnit(stationData.tcap) }} </view>
                <view class="data-box-val">
                  {{ ((stationData.tcap || 0) / 10000).toFixed(2) }}
                </view>
              </view>
              <view class="">
                <view class="data-box-title"> 累计收益(万元) </view>
                <view class="data-box-val">
                  -----
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- <view class="station-card-part4">
          <text>更新于:{{ stationData.updateDate }}</text>
        </view> -->
      </CardBox>
      <view class="power-info-card">
        <view class="power-card-box">
          <view class="power-icon">
            <image src="../../static/elk.png" mode="aspectFill"></image>
          </view>
          <view class="power-info">
            <view class="power-info-title"> 实时功率{{ getPowerUnit(stationData.acPower) }} </view>
            <view class="power-info-val">
              {{ formatPowerValue(stationData.acPower) }}
            </view>
          </view>
        </view>
        <view style="width: 1rpx; height: 60rpx; background-color: #e6e6e6"></view>
        <view class="power-card-box">
          <view class="power-icon">
            <image src="../../static/trick.png" mode="aspectFill"></image>
          </view>
          <view class="power-info">
            <view class="power-info-title">
              装机功率{{ stationData.capacity > 1000000 ? "(MWp)" : "(kWp)" }}
            </view>
            <view class="power-info-val">
              {{
                stationData.capacity > 1000000
                  ? ((stationData.capacity || 0) / 1000000).toFixed(2)
                  : ((stationData.capacity || 0) / 1000).toFixed(2)
              }}
            </view>
          </view>
        </view>
      </view>
      <TitleCardBox title="发电趋势">
        <view class="trend-date-select">
          <view class="data-subsection">
            <u-subsection activeColor="#40b780" :list="dateSelectList" :current="dateCurrent"
              @change="dateChange"></u-subsection>
          </view>
          <view class="date-num" v-if="showDateControls">
            <uni-icons type="left" size="14" @click="changeDate(-1)"></uni-icons>
            <view class="date-val">{{ formattedDate }}</view>
            <uni-icons v-if="canGoForward" type="right" size="14" @click="changeDate(1)"></uni-icons>
          </view>
        </view>
        <Area style="height: 400rpx; margin-top: 50rpx; right: 20rpx" :categories="parentCategories"
          :series="parentSeries" :trendData="trendData"></Area>
        <!-- </view> -->
      </TitleCardBox>
      <TitleCardBox title="电站减排">
        <view class="emission-container">
          <view class="emission-item" style="background-image: url('/static/images/index/tree.png')">
            <text class="number">{{ convertValue(stationData.saveTree) }}</text>
            <text class="unit">万棵</text>
          </view>
          <view class="emission-item" style="background-image: url('/static/images/index/co.png')">
            <text class="number">{{ convertValue(stationData.saveCo2) }}</text>
            <text class="unit">千吨</text>
          </view>
          <view class="emission-item" style="background-image: url('/static/images/index/waste.png')">
            <text class="number">{{ convertValue(stationData.saveCoal) }}</text>
            <text class="unit">千吨</text>
          </view>
        </view>
      </TitleCardBox>
    </view>

    <view v-show="tabsCurrent === 1" class="shebei-box">
      <CardBox v-for="item in displayedStationList" :key="item.equipmentId">
        <view class="equipment-info-card" @click="goToInverterPage(item)">
          <view class="equipment-card-part1">
            <view class="equi-part-left">
              <view class="equipment-status">
                <text class="status-text">{{ item.status.slice(0, 2) }}</text>
              </view>
              <view class="equipment-title">
                {{ item.eqName }}
              </view>
            </view>
            <view class="equi-part-right">
              <uni-icons type="bars" size="18"></uni-icons>
            </view>
          </view>
          <view class="equipment-card-part2">
            <view class="sn-show"> 逆变器 S/N:{{ item.sn }} </view>
          </view>
          <view class="equipment-card-part3">
            <view class="eq-data-bg">
              <view class="eq-param-title"> 当日发电{{ getDayUnit(item.dayElectricity) }} </view>
              <view class="eq-param-val">
                {{ formatDayValue(item.dayElectricity) }}
              </view>
            </view>
            <view class="eq-data-bg">
              <view class="eq-param-title"> 总有功功率(KW) </view>
              <view class="eq-param-val">
                {{ item.acActivePower / 1000 }}
              </view>
            </view>
          </view>
          <view class="equipment-card-part4">
            <view class="copy-sn">
              <uni-icons type="link" size="16" color="#bcbcbc"></uni-icons>
              关联的通信设备 S/N: {{ item.sn }}
            </view>
          </view>
        </view>
      </CardBox>
      <u-loadmore v-if="stationList.length > pageSize" :status="loadMoreStatus" @loadmore="loadMore"
        :icon-type="iconType" :load-text="loadText" />
    </view>

    <!-- 故障模块 -->
    <view v-show="tabsCurrent === 2" class="guzhang-box">
      <!-- 第一行：椭圆形按钮 -->
      <view class="tab-container">
        <view class="tab" :class="{ active: activeTab === 'unclosed' }" @click="changeTab('unclosed')">
          未关闭
        </view>
        <view class="tab" :class="{ active: activeTab === 'history' }" @click="changeTab('history')">
          历史故障
        </view>
        <view class="tab-line" :style="{ left: activeTab === 'unclosed' ? '0' : '50%' }"></view>
      </view>

      <!-- 第二行：图标和复选框 -->
      <view class="icon-container">
        <view class="checkbox-wrapper">
          <u-checkbox-group v-model="checkboxValues" placement="row" @change="groupChange">
            <view class="icon-item" v-for="(item, index) in checkboxList" :key="index">
              <u-checkbox :label="item.name" :name="item.name" @change="checkboxChange" shape="circle">
                <template #label>
                  <view class="checkbox-content">
                    <text class="icon-text">{{ item.name }}</text>
                  </view>
                </template>
              </u-checkbox>
            </view>
          </u-checkbox-group>
        </view>
      </view>

      <!-- 第三行：故障信息 -->
      <view style="padding: 20rpx">
        <CardBox v-for="fault in currentFaults" :key="fault.id" @click="goToFaultDetail(fault.id)">
          <view class="fault-header">
            <view class="small-button">故障</view>
            <text class="fault-text">{{ fault.faultName }}</text>
            <text class="closed-text">{{ fault.processStatusStr }}</text>
          </view>

          <view class="action-container">
            <view class="tric-demo">
              <img src="../../static/images/index/tric.png" alt="Example Image" class="small-icon" />
            </view>
            <text class="action-text">{{ fault.stationFullName }}</text>
          </view>

          <view class="action-container">
            <view class="pd-demo">
              <img src="../../static/images/index/pd.png" alt="Example Image" class="small-icon" />
            </view>
            <text class="action-text">{{ fault.eqName }}</text>
          </view>

          <view class="divider"></view>

          <view class="time-container">
            <view class="time-text">{{ formatDateTime(fault.processTime) }}</view>
          </view>
        </CardBox>
      </view>
    </view>

    <!-- 简介模块 -->
    <view v-show="tabsCurrent === 3" class="introduction-container">
      <view class="intro-content">
        <!-- 左侧图片盒子
        <view class="intro-image-box">
          <view class="border-overlay">
            <view class="coordinates">
              <text class="coordinate-text"
                >经度: {{ stationData.longtitude }} 纬度:{{
                  stationData.latitude
                }}</text
              >
            </view>
          </view>
          <image
            :src="stationData.imageUrl || '../../static/logo.png'"
            mode="aspectFill"
            class="station-img"
          >
          </image>
        </view> -->

        <!-- 右侧列表盒子 -->
        <view class="intro-list-box">
          <CardBox>
            <uni-section title="基础信息" type="line">
              <uni-list>
                <!-- 基础信息 -->
                <InfoList :items="infoListItems.map((item) => ({
                  ...item,
                  rightText: String(item.rightText || ''),
                }))
                  " :isEditing="isEditing" />
              </uni-list>
            </uni-section>
          </CardBox>

          <CardBox>
            <uni-section title="业主信息" type="line">
              <uni-list>
                <!-- 业主信息 -->
                <InfoList :items="ownerInfoItems" :isEditing="isEditing" />
              </uni-list>
            </uni-section>
          </CardBox>

        </view>
      </view>

      <!-- 修改按钮 -->
      <!--      <view class="modify-button" @click="onModifyClick">-->
      <!--        {{ isEditing ? "保存信息" : "修改信息" }}-->
      <!--      </view>-->
    </view>
  </view>
</template>
<script>
import Area from "@/components/station/area.vue";
import AreaDemo from "@/components/station/areademo.vue";
import TitleCardBox from "@/components/TitleCardBox/TitleCardBox.vue";
import CardBox from "@/components/CardBox/CardBox.vue";
import InfoList from "@/components/list/InfoList.vue";
import {
  getStationDetail,
  getEquipmentWarning,
  getStationDetailinfo,
  getEquipment,
  getStationCurveData,
  postStationInfo,
} from "@/api/station/station.js";

export default {
  components: {
    Area,
    TitleCardBox,
    CardBox,
    InfoList,
    AreaDemo,
  },
  data() {
    return {
      isEditing: false,
      currentFaults: [],
      stationData: {
        stationId: null,
        stationCode: "",
        fullName: "",
        shortName: "",
        stationType: "",
        place: "",
        longitude: null,
        latitude: null,
        weather: "",
        temperature: null,
        status: "",
      },
      activeTab: "unclosed",
      faults: [],
      checkboxValues: ["故障", "告警"],
      checkboxList: [
        {
          name: "故障",
        },
        {
          name: "告警",
        },
        {
          name: "提示",
        },
        {
          name: "建议",
        },
      ],
      stationId: null,
      tabsCurrent: 0,
      tabsList: [
        {
          name: "概览",
        },
        {
          name: "设备",
        },
        {
          name: "故障",
        },
        {
          name: "简介",
        },
      ],
      dateSelectList: ["日", "月", "年", "总"],
      dateCurrent: 0,
      chartData: {
        categories: [], // x 轴数据
        series: [], // 数据系列
      },
      opts: {
        color: [
          "#1890FF",
          "#91CB74",
          "#FAC858",
          "#EE6666",
          "#73C0DE",
          "#3CA272",
          "#FC8452",
          "#9A60B4",
          "#ea7ccc",
        ],
        padding: [15, 15, 0, 5],
        enableScroll: false,
        legend: {},
        xAxis: {
          disableGrid: true,
        },
        yAxis: {
          data: [
            {
              min: 0,
            },
          ],
        },
        extra: {
          column: {
            type: "group",
            width: 30,
            activeBgColor: "#000000",
            activeBgOpacity: 0.08,
          },
        },
      },
      trendData: {
        unit: "",
        unitName: "",
        output: [],
        xaxis: [],
        timeType: "",
      },
      parentCategories: ["00:00", "04:00", "08:00", "12:00", "16:00"],
      parentSeries: [
        {
          name: "PV",
          data: [10, 600, 40, 20, 30],
          legendShape: "circle",
        },
      ],
      stationList: [],
      waringList: [],
      infoListItems: [],
      ownerInfoItems: [],
      otherInfoItems: [],
      queryParams: {
        stationId: null,
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
        day: new Date().getDate(),
      },
      currentDate: new Date(),
      pageSize: 5, // 每页显示数量
      currentPage: 1, // 当前页码
      loadMoreStatus: 'loadmore', // loadmore状态
      iconType: 'circle',
      loadText: {
        loadmore: '点击或上拉加载更多',
        loading: '正在加载...',
        nomore: '没有更多了'
      }
    };
  },
  created() { },
  mounted() {
    this.initializeInfoListItems();
  },
  methods: {
    navLeftClick() {
      uni.switchTab({
        url: "./index",
      });
    },
    initializeInfoListItems() {
      this.infoListItems = [
        {
          title: "电站名称",
          rightText: String(this.stationData.fullName || ""),
        },
        {
          title: "电站类型",
          rightText: String(this.stationData.stationTypeStr || ""),
        },
        {
          title: "电站容量",
          rightText: String(this.stationData.capacity > 1000000 ? (this.stationData.capacity / 1000000).toFixed(3) + "(MWp)" : (this.stationData.capacity / 1000).toFixed(3) + "(kWp)"),
        },
        {
          title: "国家",
          rightText: String(this.stationData.Country || "中国"),
        },
        {
          title: "时区",
          rightText: "东八区",
        },
        {
          title: "电站地址",
          rightText: String(this.stationData.place || ""),
        },
        {
          title: "组织名称",
          rightText: String(this.stationData.organizationStr || ""),
        },
      ];
      this.ownerInfoItems = [
        {
          title: "业主手机号",
          rightText: this.stationData.phoneNum,
        },
        {
          title: "用户名",
          rightText: this.stationData.userName,
        },
      ];
    },
    convertValue(value) {
      if (!value && value !== 0) return "0.00";
      return (value / 1000).toFixed(2);
    },
    getStationDetail(stationId) {
      getStationDetail(stationId).then((response) => {
        if (response && response.data) {
          this.stationData = {
            ...this.stationData,
            ...response.data,
          };
          this.initializeInfoListItems();
        }
      });
    },
    updateFaultList() {
      if (this.activeTab === "unclosed") {
        // 清空 currentFaults，不修改 this.faults
        this.currentFaults = [];
      } else if (this.activeTab === "history") {
        // 只有当 this.faults 有值时才赋值给 currentFaults
        if (this.faults && this.faults.length > 0) {
          this.currentFaults = [...this.faults]; // 使用展开运算符确保是新数组
        } else {
          // 如果没有数据，不修改 currentFaults，或者设置为默认空值
          this.currentFaults = []; // 如果需要为空，则这样做
        }
      }
    },

    changeTab(tab) {
      this.activeTab = tab;
      this.updateFaultList();
    },

    tabsChange(e) {
      this.tabsCurrent = e.index;
      if (e.index === 2 && this.faults.length === 0) {
        this.getEquipmentWarning(this.stationId); // 确保传入正确的 stationId
      }
    },
    getStationDetailinfo(stationId) {
      getStationDetailinfo(stationId).then((response) => {
        if (response && response.data) {
          this.stationData = {
            ...this.stationData,
            ...response.data,
          };
         
          this.initializeInfoListItems();
        }
      });
    },
    getEquipment(stationId) {
      getEquipment(stationId).then((response) => {
        if (response && response.rows) {
          this.stationList = response.rows;
          this.loadMoreStatus = this.stationList.length > this.pageSize ? 'loadmore' : 'nomore';
        }
      });
    },
    goToInverterPage(item) {
      uni.navigateTo({
        url: `/pages/station/lnv?id=${item.equipmentId}`,
      });
    },
    getEquipmentWarning(stationId) {
      this.stationIds = stationId;

      getEquipmentWarning().then((response) => {
        if (response && response.rows && response.rows.length > 0) {
          const filteredFaults = response.rows.filter((fault) => {
            const isMatch = fault.stationId == stationId;

            return isMatch;
          });

          if (filteredFaults.length > 0) {
            this.faults = filteredFaults;

            console.log("故障列表：", this.faults);

            this.updateFaultList();
          } else {
          }
        } else {
        }
      });

      getEquipmentWarning().then((response) => {
        if (response && response.rows && response.rows.length > 0) {
          // 遍历 rows 数组并检查每个 item 的 stationId
          const filteredFaults = response.rows.filter((fault) => {
            const isMatch = fault.stationId === stationId;
            return isMatch;
          });
          if (filteredFaults.length > 0) {
            this.faults = filteredFaults;
            this.updateFaultList();
          } else {
            console.warn("未找到匹配的 stationId 数据");
          }
        } else {
          console.warn("未获取到有效的 response.rows 数据");
        }
      });
    },

    onLoad(options) {
      this.stationId = options.stationId;
      this.stationData.stationId = options.stationId;
      this.queryParams.stationId = options.stationId;

      this.getStationDetail(this.stationData.stationId);
      this.getStationDetailinfo(this.stationData.stationId);
      this.getEquipment(this.stationId);
      this.getEquipmentWarning(this.stationId);
      this.getTrendData();
    },
    dateChange(index) {
      this.dateCurrent = index;
      const today = new Date();

      // 重置 currentDate 为今天的日期
      this.currentDate = new Date(today);

      // 更新查询参数
      this.queryParams.stationId = this.stationId; // 确保带上电站id

      switch (index) {
        case 0: // 日视图 - 获取当天数据
          this.queryParams = {
            stationId: this.stationId,
            year: today.getFullYear(),
            month: today.getMonth() + 1,
            day: today.getDate(),
          };
          break;
        case 1: // 月视图 - 获取当月数据
          this.queryParams = {
            stationId: this.stationId,
            year: today.getFullYear(),
            month: today.getMonth() + 1,
          };
          break;
        case 2: // 年视图 - 获取当年数据
          this.queryParams = {
            stationId: this.stationId,
            year: today.getFullYear(),
          };
          break;
        case 3: // 总视图
          this.queryParams = {
            stationId: this.stationId,
          };
          break;
      }

      this.getTrendData();
    },
    onModifyClick() {
      if (this.isEditing) {
        // 如果是编辑状态，保存数据
        this.saveData();
      } else {
        // 切换到编辑状态
        this.isEditing = true;
      }
    },
    async saveData() {
      try {
        // 调用接口保存数据
        const response = await this.saveStationData({
          stationData: this.stationData,
          infoListItems: this.infoListItems,
          ownerInfoItems: this.ownerInfoItems,
          otherInfoItems: this.otherInfoItems,
        });

        this.isEditing = false; // 保存成功后，切换到查看模式
      } catch (error) {
        console.error("保存失败:", error);
      }
    },
    // 保存电站数据，调用 API 请求
    async saveStationData(data) {
      try {
        // 调用封装的 API 请求
        const response = await postStationInfo(data); // 使用 postStationInfo 发送数据
        return response; // 返回响应结果
      } catch (error) {
        throw new Error("保存失败: " + error.message); // 如果请求失败，抛出错误
      }
    },
    changeDate(direction) {
      const date = new Date(this.currentDate);
      const today = new Date();

      switch (this.dateCurrent) {
        case 0: // 日视图
          date.setDate(date.getDate() + direction);
          // 限制不能超过今天
          if (date > today) {
            date.setTime(today.getTime());
          }
          break;

        case 1: // 月视图
          date.setMonth(date.getMonth() + direction);
          // 限制不能超过当前月
          if (
            date.getFullYear() > today.getFullYear() ||
            (date.getFullYear() === today.getFullYear() &&
              date.getMonth() > today.getMonth())
          ) {
            date.setFullYear(today.getFullYear());
            date.setMonth(today.getMonth());
          }
          break;

        case 2: // 年视图
          date.setFullYear(date.getFullYear() + direction);
          // 限制不能超过当前年
          if (date.getFullYear() > today.getFullYear()) {
            date.setFullYear(today.getFullYear());
          }
          break;
      }

      this.currentDate = date;

      // 更新查询参数
      this.queryParams.stationId = this.stationId; // 确保带上电站id
      switch (this.dateCurrent) {
        case 0: // 日视图
          this.queryParams = {
            stationId: this.stationId,
            year: date.getFullYear(),
            month: date.getMonth() + 1,
            day: date.getDate(),
          };
          break;
        case 1: // 月视图
          this.queryParams = {
            stationId: this.stationId,
            year: date.getFullYear(),
            month: date.getMonth() + 1,
          };
          break;
        case 2: // 年视图
          this.queryParams = {
            stationId: this.stationId,
            year: date.getFullYear(),
          };
          break;
      }

      this.getTrendData(); // 获取趋势数据
    },

    goToFaultDetail(faultId) {
      if (faultId) {
        uni.navigateTo({
          url: `/pages/warn/StationFaultPage?id=${faultId}`,
        });
      } else {
        console.error("故障ID无效");
      }
    },
    getTrendData() {
      getStationCurveData(this.queryParams)
        .then((res) => {
          if (res.code === 200 && res.data) {
            let outputData = [];
            let xaxisData = [];
            this.parentSeries = [
              {
                name: "PV",
                data: outputData,
                legendShape: "circle",
              },
            ];

            // 处理不同的日期视图
            switch (this.dateCurrent) {
              case 0: // 日视图
                outputData = res.data.acPower || [];
                xaxisData = res.data.xaxis || [];
                if (Math.max(...outputData) > 1000) {
                  //将数值转化为kw基础单位
                  outputData = outputData.map((val) =>
                    Number((val / 1000).toFixed(2))
                  );
                  this.trendData.unit = "kW"; // 日视图的单位是 kW
                } else {
                  this.trendData.unit = "W"; // 日视图的单位是 W
                }
                this.trendData.unitName = "功率"; // 单位名称
                this.parentSeries = [
                  {
                    name: "功率",
                    data: outputData,
                    legendShape: "circle",
                  },
                ];
                break;

              case 1: // 月视图
              case 2: // 年视图
              case 3: // 总视图
                outputData = res.data.output || [];
                xaxisData = res.data.xaxis || [];

                // 如果数值较大，转换为万度
                if (Math.max(...outputData) > 10000) {
                  outputData = outputData.map((val) =>
                    Number((val / 10000).toFixed(2))
                  );
                  this.trendData.unit = "万度"; // 万度单位
                } else {
                  this.trendData.unit = "度"; // KWh单位
                }
                this.trendData.unitName = "发电量"; // 单位名称
                this.parentSeries = [
                  {
                    name: "发电量",
                    data: outputData,
                    legendShape: "circle",
                  },
                ];
                break;
            }

            // 更新图表数据
            this.parentCategories = xaxisData;

            // 更新趋势数据
            this.trendData = {
              ...this.trendData,
              output: outputData,
              xaxis: xaxisData,
              timeType: ["day", "month", "year", "total"][this.dateCurrent],
            };
          } else {
            uni.showToast({
              title: res.msg || "获取数据失败",
              icon: "none",
            });
          }
        })
        .catch((error) => {
          uni.showToast({
            title: "获取数据失败",
            icon: "none",
          });
        });
    },
    // 获取功率单位
    getPowerUnit(value) {
      if (!value || isNaN(value)) return '(kW)';
      return value >= 1000000 ? '(MW)' : '(kW)';
    },

    // 格式化功率值
    formatPowerValue(value) {
      if (!value || isNaN(value)) {
        return '0.00';
      }
      if (value >= 1000000) {
        return (value / 1000000).toFixed(2); // 转换为 MW
      }
      return (value / 1000).toFixed(2); // 转换为 kW
    },
    // 格式化时间方法
    formatDateTime(timestamp) {
      if (!timestamp) return '';

      try {
        // 处理带T和时区的ISO格式时间字符串
        const date = new Date(timestamp);

        // 格式化年月日
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        // 格式化时分秒
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        // 返回格式化后的时间字符串
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      } catch (error) {
        console.error('时间格式转换错误:', error);
        return timestamp; // 如果转换失败则返回原始值
      }
    },
    // 格式化容量显示
    formatCapacity(value) {
      if (!value || isNaN(value)) return '0';
      return value > 10000 ?
        `${(value / 10000).toFixed(2)}万度` :
        `${value}度`;
    },

    // 获取单位
    getDayUnit(value) {
      if (!value || isNaN(value)) return '(度)';
      return value > 10000 ? '(万度)' : '(度)';
    },

    // 格式化数值
    formatDayValue(value) {
      if (!value || isNaN(value)) return '0.00';
      return value > 10000 ?
        (value / 10000).toFixed(2) :
        value.toFixed(2);
    },
    formatUnit(value) {
      if (!value || isNaN(value)) return '';
      if (value >= 1000000) return 'MW';
      return 'kW';
    },
    formatValue(value) {
      if (!value || isNaN(value)) return '0.00';
      if (value >= 1000000) return (value / 1000000).toFixed(2);
      return (value / 1000).toFixed(2);
    },
    loadMore() {
      if (this.displayedStationList.length >= this.stationList.length) {
        this.loadMoreStatus = 'nomore';
        return;
      }

      this.loadMoreStatus = 'loading';
      setTimeout(() => {
        this.currentPage += 1;
        this.loadMoreStatus = this.displayedStationList.length >= this.stationList.length ? 'nomore' : 'loadmore';
      }, 300);
    },
  },

  computed: {
    formattedDate() {
      const date = this.currentDate;
      switch (this.dateCurrent) {
        case 0: // 日
          return date.toISOString().split("T")[0]; // YYYY-MM-DD
        case 1: // 月
          return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
            2,
            "0"
          )}`; // YYYY-MM
        case 2: // 年
          return `${date.getFullYear()}`; // YYYY
        default:
          return "";
      }
    },
    showDateControls() {
      return this.dateCurrent !== 3; // 如果不是"总"视图就显示日期控制
    },
    canGoForward() {
      if (this.dateCurrent === 3) return false;

      const today = new Date();
      const current = this.currentDate;

      switch (this.dateCurrent) {
        case 0: // 日视图
          return current.getTime() < today.setHours(0, 0, 0, 0);
        case 1: // 月视图
          return (
            current.getFullYear() < today.getFullYear() ||
            (current.getFullYear() === today.getFullYear() &&
              current.getMonth() < today.getMonth())
          );
        case 2: // 年视图
          return current.getFullYear() < today.getFullYear();
        default:
          return false;
      }
    },
    displayedStationList() {
      return this.stationList.slice(0, this.currentPage * this.pageSize);
    }
  },
};
</script>



<style lang="scss">
.emission-container {
  display: flex;
  flex-wrap: wrap;
  /* 允许换行 */
  justify-content: space-around;
  /* 均匀分布项 */
}

.emission-item {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 30%;
  /* 设置每个项的宽度为30% */
  height: 180rpx;
  /* 设置高度 */
  background-size: cover;
  /* 背景图像覆盖 */
  background-position: center;
  /* 背景图像居中 */
  margin-bottom: 5rpx;
  /* 增加底部间距 */
  color: #fff;
  /* 字体颜色 */
  font-size: 24rpx;
  /* 字体大小 */
  text-align: center;
  /* 使文本居中 */
  border-radius: 8rpx;
  /* 圆角 */
}

.number {
  font-size: 32rpx;
  /* 数字放大 */
  font-weight: bold;
  /* 加粗 */
  margin-right: 10rpx;
  /* 增加右侧间距 */
  color: #333;
  margin-bottom: 50rpx;
}

.unit {
  font-size: 24rpx;
  /* 单位大小不变 */
  color: #333;
  /* 单位颜色 */
  margin-bottom: 50rpx;
  margin-left: 0;
  /* 去掉左侧间距 */
}

.top-tabs {
  background-color: #fff;
  padding: 0 90rpx;
}

.gailan-box {
  padding: 20rpx;

  .station-info-card {
    display: flex;
    flex-direction: column;

    .station-card-part1 {
      display: flex;
      justify-content: space-between;
      width: 100%;

      .weather-data {
        color: #bcbcbc;
      }

      .station-status {
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 8rpx;
        width: 90rpx;
        height: 40rpx;
        background-color: #e6f6f1;

        .status-text {
          color: #4fbb9d;
          font-size: 24rpx;
          margin-bottom: 3rpx;
        }
      }
    }

    .station-card-part2 {
      margin-top: 40rpx;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20rpx;

      .data-bg {
        background-color: #f8f8f8;
        border-radius: 16rpx;
        padding: 30rpx;

        .param-title {
          font-size: 32rpx;
          color: #000000;
          word-wrap: 2rpx;
        }

        .param-val,
        .param-val1 {
          color: #000000;
          font-size: 50rpx;
          font-weight: bold;
          margin-top: 24rpx;
          position: relative;
          width: auto;
          display: inline-block;
          z-index: 1;
        }

        .param-val::after,
        .param-val1::after {
          content: "";
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 20rpx;
          background-color: #b4ddd5;
          z-index: -1;
        }

        .param-val1::after {
          background-color: #f1dca5;
        }
      }
    }

    .station-card-part3 {
      margin-top: 44rpx;
      display: flex;
      justify-content: space-around;

      .part3-box {
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;

        .station-data-box {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          height: 210rpx;

          .data-box-title {
            font-size: 28rpx;
            word-wrap: 4rpx;
            color: #bcbcbc;
          }

          .data-box-val {
            font-size: 34rpx;
            color: #000000;
            font-weight: bold;
            margin-top: 10rpx;
          }
        }
      }
    }

    .station-card-part4 {
      margin-top: 40rpx;
      font-size: 28rpx;
      color: #bcbcbc;
      display: flex;
      justify-content: flex-end;
    }
  }

  .power-info-card {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background-color: #ffffff;
    padding: 36rpx 30rpx;
    border-radius: 20rpx;
    margin-bottom: 20rpx;

    .power-card-box {
      display: flex;
      align-items: center;

      .power-icon {
        background-color: #fff5ed;
        border-radius: 50%;
        width: 80rpx;
        height: 80rpx;
        display: flex;
        justify-content: center;
        align-items: center;

        image {
          width: 60%;
          height: 60%;
        }
      }

      .power-info {
        display: flex;
        flex-direction: column;
        margin-left: 30rpx;

        .power-info-title {
          font-size: 28rpx;
          color: #000000;
        }

        .power-info-val {
          font-size: 38rpx;
          font-weight: bold;
          margin-top: 10rpx;
        }
      }
    }
  }

  .trend-date-select {
    .data-subsection {}

    .date-num {
      margin-top: 20rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      .date-val {
        font-size: 24rpx;
        margin: 0 20rpx;
      }
    }

    .data-show {
      // 数据展示域样式
    }
  }
}

.shebei-box {
  padding: 20rpx;

  .equipment-info-card {
    .equipment-card-part1 {
      display: flex;
      justify-content: space-between;

      .equi-part-left {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .equipment-status {
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 8rpx;
          width: 60rpx;
          height: 30rpx;
          background-color: #e6f6f1;

          .status-text {
            color: #4fbb9d;
            font-size: 15rpx;
            font-weight: bold;
          }
        }

        .equipment-title {
          margin-left: 20rpx;
          font-size: 30rpx;
          font-weight: bold;
        }
      }

      .equi-part-right {}
    }

    .equipment-card-part2 {
      margin-top: 18rpx;

      .sn-show {
        font-size: 24rpx;
        color: #bcbcbc;
      }
    }

    .equipment-card-part3 {
      margin-top: 18rpx;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20rpx;

      .eq-data-bg {
        background-color: #f8f8f8;
        border-radius: 16rpx;
        padding: 30rpx;

        .eq-param-title {
          font-size: 26rpx;
          color: #bcbcbc;
          word-wrap: 2rpx;
        }

        .eq-param-val {
          color: #000000;
          font-size: 30rpx;
          font-weight: bold;
          margin-top: 16rpx;
        }
      }
    }

    .equipment-card-part4 {
      margin-top: 16rpx;

      .copy-sn {
        display: flex;
        align-items: center;
        font-size: 22rpx;
        color: #bcbcbc;
      }
    }
  }
}

.introduction-container {
  display: flex;
  flex-direction: column;
  /* 垂直排列 */
  padding: 10rpx;
}

.intro-content {
  display: flex;
  /* 使用 Flexbox 布局 */
  flex-direction: column;
  /* 左右排列 */
  justify-content: space-between;
  /* 两边对齐 */
}

.intro-image-box {
  width: 100%;
  /* 左侧图片占45%宽度 */
  text-align: center;
  /* 图片居中 */
  position: relative;
  /* 确保绝对定位相对于此容器 */
}

.intro-list-box {
  width: 100%;
  /* 右侧列表占50%宽度 */
  border-radius: 100rpx;
  /* 圆角 */
}

.station-img {
  width: 100%;
  /* 图片宽度自适应 */
  border-radius: 20rpx;
  /* 圆角 */
  margin-bottom: 20rpx;
}

.border-overlay {
  position: absolute;
  top: 3%;
  left: 50%;
  transform: translateX(-50%);
  padding: 0rpx 10rpx;
  border-radius: 55rpx;
  background-color: rgba(255, 255, 255, 0.5);
  z-index: 1;
  width: 90%;
  max-width: 470rpx;
  box-sizing: border-box;
  white-space: nowrap;
  /* 不换行 */
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
}

.coordinates {
  text-align: center;
  /* 文本居中 */
  color: black;
  /* 字体颜色 */
  font-size: 26rpx;
  /* 字体大小 */
}

.modify-button {
  margin-top: 20rpx;
  /* 修改按钮的上边距 */
  text-align: center;
  /* 按钮居中 */
  padding: 20rpx;
  background-color: #f09336;
  /* 按钮背景颜色 */
  color: #fff;
  /* 按钮文字颜色 */
  border-radius: 55rpx;
  /* 按钮圆角 */
}

.container {
  display: flex;
  flex-direction: column;
  padding: 0rpx;
  background-color: #f5f5f5;
  width: 100%;
}

.tab-container {
  position: relative;
  display: flex;
  justify-content: center;
  background-color: #fff;
  flex-wrap: wrap;
  /* 支持换行 */
}

.tab {
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: color 0.3s;
  height: 60rpx;
  /* 增加高度 */
  min-width: 80rpx;
  /* 最小宽度以适应不同屏幕 */
}

.tab.active {
  color: rgb(58, 189, 58);
}

.tab-line {
  position: absolute;
  bottom: 0;
  height: 8rpx;
  /* 底线高度 */
  background-color: rgb(58, 189, 58);
  width: 10%;
  left: 0;
  transform: translateX(200%);
  transition: left 0.3s;
}

.icon-container {
  display: flex;
  justify-content: center;
  /* 居中对齐 */
  padding: 1rpx;
  /* 添加内边距 */
}

.checkbox-wrapper {
  background-color: #fff;
  /* 背景颜色 */
  padding: 20rpx;
  /* 内边距 */
  width: 100%;
  /* 使其占满父容器 */
}

.u-checkbox-group {
  display: flex;
  /* 使用 flex 布局 */
  justify-content: space-between;
  /* 均匀分布 */
  flex-wrap: wrap;
  /* 换行 */
}

.icon-item {
  flex: 1;
  /* 每个复选框占据相同空间 */
  min-width: 80rpx;
  /* 设置最小宽度 */
  max-width: 150rpx;
  /* 设置最大宽度 */
  margin: 1rpx;
  /* 增加间距 */
  display: flex;
  /* 使内容居中 */
  justify-content: center;
  /* 居中对齐 */
  align-items: center;
  /* 垂直居中 */
}

.icon-text {
  font-size: 26rpx;
  /* 字体大小 */
  color: #333;
}

.fault-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10rpx;
  width: 100%;
}

.small-button {
  background-color: #f6f6f8;
  color: #c0bfca;
  padding: 0 10rpx;
  border-radius: 5rpx;
  margin-right: 10rpx;
  border: none;
  font-size: 16rpx;
  font-weight: bold;
}

.fault-text {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  /* 让文本自适应 */
  text-align: left;
  /* 确保文本左对齐 */
  font-weight: bold;
}

.closed-text {
  font-size: 16rpx;
  color: #999;
  margin-left: 0;
}

.action-container {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  border-radius: 35rpx;
}

.small-icon {
  margin-left: 10rpx;
  width: 30rpx;
  height: 30rpx;
}

.action-text {
  font-size: 22rpx;
  color: #aaa;
  margin-left: 12rpx;
}

.divider {
  height: 1rpx;
  background-color: #ddd;
  margin: 12rpx 0;
}

.time-container {
  text-align: left;
  width: 100%;
  /* 确保容器占满宽度 */
}

.time-text {
  font-size: 25rpx;
  color: #999;
  flex: 1;
  /* 使文本自适应 */
  text-align: right;
  /* 右对齐 */
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.info-label {
  font-size: 26rpx;
  color: #333;
}

.info-value {
  font-size: 26rpx;
  color: #333;
}
</style>
