<template>
	<view class="container">

		<view v-if="InvTabsCurrent === 0" style="padding: 20rpx">

			<CardBox>
				<InfoList :items="infoListItems" />
			</CardBox>

			<CardBox>
				<CollapsePanel :items="collapsePanelItems" />
			</CardBox>

			<CardBox>
				<uni-collapse class="custom-collapse trend-collapse">
					<uni-collapse-item title="运行趋势" accordion="true" :open="true">
						<view class="run-trend">
							<view class="run-trend-part1">
								<view class="date-selector-container">
									<view class="data-subsection">
										<u-subsection activeColor="#40b780" bgColor="#f5f5f5" :list="dateSelectList"
											:current="dateCurrent" @change="dateChange"
											buttonStyle="border-radius: 50rpx; font-weight: 500; box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);">
										</u-subsection>
									</view>
									<view class="date-num">
										<view class="date-arrow" @click="changeDate(-1)">
											<uni-icons type="left" size="16" color="#40b780"></uni-icons>
										</view>
										<view class="date-val">{{ currentDate }}</view>
										<view class="date-arrow" @click="changeDate(1)">
											<uni-icons type="right" size="16" color="#40b780"></uni-icons>
										</view>
									</view>
								</view>
							</view>
							<view class="run-trend-part2">
								<view class="chart-tabs">
									<u-tabs :list="AttributeColumnTabsList" :is-scroll="false"
										:current="AttributeColumnTabsCurrent" :scrollable="false" lineColor="#40b780"
										:activeStyle="{
											color: '#40b780',
											fontSize: '30rpx',
											fontWeight: 'bold',
											transform: 'scale(1.02)'
										}" :inactiveStyle="{
											color: '#606266',
											fontSize: '28rpx',
											transform: 'scale(1)'
										}" itemStyle="height: 90rpx; padding: 0 10rpx;" @change="AttributeColumnTabsChange">
									</u-tabs>
								</view>

								<view class="chart-container">
									<InverterChart
										:chartData="chartData"
										:leftAxisLabel="getLeftAxisLabel()"
										:rightAxisLabel="getRightAxisLabel()"
										:showRightAxis="showRightAxis"
										:legendItems="getLegendItems()"
										:title="getChartTitle(AttributeColumnTabsCurrent, currentDataType)"
										@chart-type-changed="onChartTypeChanged" />
								</view>


								<view class="data-type-selector"
									v-if="AttributeColumnTabsCurrent === 1 || AttributeColumnTabsCurrent === 2">
									<view class="radio-group">
										<view class="radio-item" :class="{ active: currentDataType === 'current' }"
											@click="switchDataType('current')">
											<view class="radio-circle">
												<view class="radio-inner" v-if="currentDataType === 'current'"></view>
											</view>
											<text>{{ AttributeColumnTabsCurrent === 1 ? 'PV电流 (A)' : '相电流 (A)' }}</text>
										</view>
										<view class="radio-item" :class="{ active: currentDataType === 'voltage' }"
											@click="switchDataType('voltage')">
											<view class="radio-circle">
												<view class="radio-inner" v-if="currentDataType === 'voltage'"></view>
											</view>
											<text>{{ AttributeColumnTabsCurrent === 1 ? 'PV电压 (V)' : '相电压 (V)' }}</text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</uni-collapse-item>
				</uni-collapse>
			</CardBox>

			<CardBox>
				<CollapsePanel :items="dataitems" />
			</CardBox>


			<CardBox>
				<uni-collapse class="custom-collapse">
					<uni-collapse-item title="PV数据" accordion :open="true">
						<view class="data-table pv-table">
							<view class="table-header">
								<view v-for="(header, index) in pvTableData.headers" :key="index" class="header-cell">
									{{ header }}
								</view>
							</view>

							<view v-for="(item, index) in displayedPvData" :key="index" class="table-row" :class="{ 'even-row': index % 2 === 1 }" v-if="item">
								<view class="table-cell pv-id">{{ item.id || `PV${index + 1}` }}</view>
								<view class="table-cell">{{ item.Current || '0.00' }}</view>
								<view class="table-cell">{{ item.Voltage || '0.00' }}</view>
								<view class="table-cell">{{ item.Power || '0.00' }}</view>
							</view>

							<view v-if="pvTableData.data.length > pvPageSize" class="show-more"
								@click="toggleShowMorePv">
								{{ showAllPv ? '收起' : '更多' }}
								<uni-icons :type="showAllPv ? 'top' : 'bottom'" size="14"></uni-icons>
							</view>

							<view v-if="!pvTableData.data || pvTableData.data.length === 0" class="no-data">
								暂无数据
							</view>
						</view>
					</uni-collapse-item>
				</uni-collapse>
			</CardBox>

			<CardBox>
				<uni-collapse class="custom-collapse">
					<uni-collapse-item title="交流信息" accordion :open="true">
						<view class="data-table ac-table">
							<view class="table-header">
								<view v-for="(header, index) in evTableData.headers" :key="index" class="header-cell">
									{{ header }}
								</view>
							</view>

							<view v-for="(item, index) in evTableData.data" :key="index" class="table-row" :class="{ 'even-row': index % 2 === 1 }" v-if="item">
								<view class="table-cell phase-id">{{ item.id || `PV${index + 1}` }}</view>
								<view class="table-cell">{{ item.Current || '0.00' }}</view>
								<view class="table-cell">{{ item.Voltage || '0.00' }}</view>
							</view>

							<view v-if="!evTableData.data || evTableData.data.length === 0" class="no-data">
								暂无数据
							</view>
						</view>
					</uni-collapse-item>
				</uni-collapse>
			</CardBox>



			<CardBox>
				<uni-collapse class="custom-collapse">
					<uni-collapse-item title="MPPT数据" accordion :open="true">
						<view class="data-table mppt-table">
							<view class="table-header">
								<view v-for="(header, index) in mpptTableData.headers" :key="index" class="header-cell">
									{{ header }}
								</view>
							</view>

							<view v-for="(item, index) in displayedMpptData" :key="index" class="table-row" :class="{ 'even-row': index % 2 === 1 }" v-if="item">
								<view class="table-cell mppt-id">{{ item.id }}</view>
								<view class="table-cell">{{ item.Current || '0.00' }}</view>
								<view class="table-cell">{{ item.Voltage || '0.00' }}</view>
							</view>

							<view v-if="mpptTableData.data.length > pageSize" class="show-more" @click="toggleShowMore">
								{{ showAllMppt ? '收起' : '更多' }}
								<uni-icons :type="showAllMppt ? 'top' : 'bottom'" size="14"></uni-icons>
							</view>

							<view v-if="!mpptTableData.data || mpptTableData.data.length === 0" class="no-data">
								暂无数据
							</view>
						</view>
					</uni-collapse-item>
				</uni-collapse>
			</CardBox>

		</view>



	</view>
</template>
<script>
import CardBox from "@/components/CardBox/CardBox.vue";
import TitleCardBox from "@/components/TitleCardBox/TitleCardBox.vue";
import InfoList from "@/components/list/InfoList.vue";
import CollapsePanel from "@/components/list/CollapsePanel.vue";
import Tablelist from '@/components/list/Tablelist.vue';
import InverterChart from '@/components/station/InverterChart.vue';
import {
	getDeviceOverview,
	getEquipmentBasicData,
	getEquipmentPVData,
	getEquipmentOtherData,
	getEquipmentMPPTData,
} from "@/api/station/equipment.js";
import {
	getEquipmentCurveData,
} from "@/api/station/station.js";
import {
	formatDate,
	formatYearMonth,
	formatTimeArray,
	getChartTitle,
	getLeftAxisLabel,
	getRightAxisLabel,
	getLegendItems as getChartLegendItems,
	getLegendTitle,
	getDateParams,
	getChartDataTypeParams,
	processPVData,
	processMPPTData,
	processACData,
	processChartData,
	getChartColors,
	debounce,
	throttle
} from "./lnv/inv.js";

export default {
	components: {
		CardBox,
		TitleCardBox,
		InfoList,
		CollapsePanel,
		Tablelist,
		InverterChart,
	},
	data() {
		return {
			tableItems: [],
			chatItems: [],
			InvTabsCurrent: 0,
			tabLinePosition: "0%",
			equipmentId: "",
			equipmentBasicData: {},
			stationData: {},
			infoListItems: [],
			collapsePanelItems: [],
			dataitems: [],
			eletricitems: [],
			dataeletric: {},
			equipitems: [],

			dateSelectList: ["日", "月", "年", "总"],
			dateCurrent: 0,
			evTableData: {
				title: "交流信息",
				headers: ["相", "电流(A)", "电压(V)"],
				data: []
			},
			pvTableData: {
				title: "PV数据",
				headers: ["PV", "电流(A)", "电压(V)", "功率(W)",],
				data: []
			},
			mpptTableData: {
				title: "MPPT数据",
				headers: ["MPPT", "电流(A)", "电压(V)",],
				data: []
			},
			AttributeColumnTabsCurrent: 0,
			AttributeColumnTabsList: [{
				name: "功率"
			},
			{
				name: "直流"
			},
			{
				name: "交流"
			}
			],

			SettingWinOpen: false,
			SettingWinWidth: 300,
			chartType: "line",
			showRightAxis: false,
			chartData: {
				categories: [],
				series: []
			},
			powerData: {
				categories: [],
				series: [
					{
						name: "总直流功率",
						data: []
					}
				]
			},
			dcData: {
				categories: [],
				series: [
					{
						name: "PV1",
						data: [],
						format: function (val) {
							return val.toFixed(2) + "A";
						}
					},
					{
						name: "PV2",
						data: [],
						format: function (val) {
							return val.toFixed(2) + "A";
						}
					},
					{
						name: "PV3",
						data: [],
						format: function (val) {
							return val.toFixed(2) + "A";
						}
					}
				],
				voltage: [
					{
						name: "PV1电压",
						data: [],
						format: function (val) {
							return val.toFixed(2) + "V";
						}
					},
					{
						name: "PV2电压",
						data: [],
						format: function (val) {
							return val.toFixed(2) + "V";
						}
					},
					{
						name: "PV3电压",
						data: [],
						format: function (val) {
							return val.toFixed(2) + "V";
						}
					}
				],
			},
			acData: {
				categories: [],
				series: [
					{
						name: "A相电流",
						data: [],
						format: function (val) {
							return val.toFixed(2) + "A";
						}
					},
					{
						name: "B相电流",
						data: [],
						format: function (val) {
							return val.toFixed(2) + "A";
						}
					},
					{
						name: "C相电流",
						data: [],
						format: function (val) {
							return val.toFixed(2) + "A";
						}
					}
				],
				voltage: [
					{
						name: "AB相电压",
						data: [],
						format: function (val) {
							return val.toFixed(2) + "V";
						}
					},
					{
						name: "BC相电压",
						data: [],
						format: function (val) {
							return val.toFixed(2) + "V";
						}
					},
					{
						name: "CA相电压",
						data: [],
						format: function (val) {
							return val.toFixed(2) + "V";
						}
					}
				]
			},
			pageSize: 4,
			showAllMppt: false,
			pvPageSize: 4,
			showAllPv: false,
			currentDate: formatDate(new Date()), // 使用工具函数
			currentDataType: 'current',
			legendColors: [
				'#2ca02c', '#1f77b4', '#ff7f0e', '#9467bd', '#d62728',
				'#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf',
				'#aec7e8', '#ffbb78', '#98df8a', '#ff9896', '#c5b0d5',
				'#c49c94', '#f7b6d2', '#c7c7c7'
			],

			acPhaseColors: {
				'A相电流': '#FF5733',
				'B相电流': '#33A8FF',
				'C相电流': '#9D33FF',
				'A相电压': '#FF5733',
				'B相电压': '#33A8FF',
				'C相电压': '#9D33FF'
			},
			// 移除加载状态
			chartLoading: false,
		};
	},

	computed: {
		displayedMpptData() {
			if (this.showAllMppt) {
				return this.mpptTableData.data;
			}
			return this.mpptTableData.data.slice(0, this.pageSize);
		},
		displayedPvData() {
			if (this.showAllPv) {
				return this.pvTableData.data;
			}
			return this.pvTableData.data.slice(0, this.pvPageSize);
		}
	},

	created() {

		this.debouncedUpdateChartData = debounce(this.updateChartData, 400, true);

		this.debouncedUpdateChartDataFromResponse = debounce(this.updateChartDataFromResponse, 400);

		this.throttledAttributeColumnTabsChange = throttle(this.handleAttributeColumnTabsChange, 500);
		this.throttledSwitchDataType = throttle(this.handleSwitchDataType, 400);
	},

	mounted() {
		this.updateItems();
		if (this.equipmentId) {
			this.getDeviceOverview(this.equipmentId);
		}

		// 获取当天的日期参数
		const currentDate = new Date();
		this.getEquipmentCurveData(this.equipmentId, {
			year: currentDate.getFullYear(),
			month: currentDate.getMonth() + 1,
			day: currentDate.getDate(),
			type: 'day'
		});
	},

	methods: {

		InvTabsChange(e) {
			this.InvTabsCurrent = e.index;
		},

		updateItems() {

			this.infoListItems = [{
				title: "序列号(S/N)",
				rightText: this.stationData.sn || "无数据"
			},
			{
				title: "运行状态",
				rightText: this.stationData.status || "无数据"
			},

			];


			this.dataitems = [{
				title: "其他信息",
				details: [

					{
						title: "设备型号",
						rightText: this.dataeletric.model || "无数据"
					},
				]
			}];


			this.collapsePanelItems = [{
				title: "概览信息",
				details: [{
					title: "当日发电",
					rightText: (this.equipmentBasicData.dayElectricity || 0) + " 度"
				},
				{
					title: "累计发电",
					rightText: ((this.equipmentBasicData.totalGeneratingCapacity || 0) / 10000).toFixed(2) + " 万度"
				},
				{
					title: "总有功功率",
					rightText: ((this.equipmentBasicData.acActivePower || 0) / 1000).toFixed(2) + " kW"
				},
				{
					title: "总直流功率",
					rightText: ((this.equipmentBasicData.dcPower || 0) / 1000).toFixed(2) + " kW"
				},
				{
					title: "总无功功率",
					rightText: ((this.equipmentBasicData.acReactivePower || 0) / 1000).toFixed(2) + " kW"
				},
				{
					title: "电网频率",
					rightText: String(this.equipmentBasicData.inverterEfficiency || "无数据")
				}
				]
			}];


		},

		// 运行趋势发电
		dateChange(index) {
			this.dateCurrent = index;
			const currentDate = new Date();


			switch (index) {
				case 0: // 日
					this.currentDate = formatDate(currentDate);
					this.getEquipmentCurveData(this.equipmentId, {
						year: currentDate.getFullYear(),
						month: currentDate.getMonth() + 1,
						day: currentDate.getDate(),
						type: 'day'
					});
					break;

				case 1: // 月
					this.currentDate = formatYearMonth(currentDate);
					this.getEquipmentCurveData(this.equipmentId, {
						year: currentDate.getFullYear(),
						month: currentDate.getMonth() + 1,
						type: 'month'
					});
					break;

				case 2: // 年
					this.currentDate = String(currentDate.getFullYear());
					this.getEquipmentCurveData(this.equipmentId, {
						year: currentDate.getFullYear(),
						type: 'year'
					});
					break;

				case 3: // 总
					this.currentDate = '总计';
					this.getEquipmentCurveData(this.equipmentId, {
						type: 'total'
					});
					break;
			}
		},

		// 运行趋势属性选择栏 - 使用节流处理
		AttributeColumnTabsChange(e) {
			// 使用节流函数来处理切换
			this.throttledAttributeColumnTabsChange(e);
		},

		// 实际处理选项卡切换的方法
		handleAttributeColumnTabsChange(e) {
			// 设置新值
			this.AttributeColumnTabsCurrent = e.index;

			// 立即更新图表结构，减少闪烁
			this.updateChartData();

			const currentDate = new Date(this.currentDate);

			// 根据当前选择的时间维度获取数据
			switch (this.dateCurrent) {
				case 0: // 日
					this.getEquipmentCurveData(this.equipmentId, {
						year: currentDate.getFullYear(),
						month: currentDate.getMonth() + 1,
						day: currentDate.getDate(),
						type: 'day'
					});
					break;
				case 1: // 月
					this.getEquipmentCurveData(this.equipmentId, {
						year: currentDate.getFullYear(),
						month: currentDate.getMonth() + 1,
						type: 'month'
					});
					break;
				case 2: // 年
					this.getEquipmentCurveData(this.equipmentId, {
						year: currentDate.getFullYear(),
						type: 'year'
					});
					break;
				case 3: // 总
					this.getEquipmentCurveData(this.equipmentId, {
						type: 'total'
					});
					break;
			}
		},

		// 打开设置窗口
		openRunSettingWin() {
			this.SettingWinOpen = true;
		},

		// 关闭设置窗口
		closeSettingWin() {
			this.SettingWinOpen = false;
		},
		// 加载设备接口数据
		onLoad(options) {
			this.equipmentId = options.id;
			console.log("接收到的 equipmentId:", this.equipmentId);
			this.getDeviceOverview(this.equipmentId);
			this.getEquipmentBasicData(this.equipmentId);
			this.getEquipmentPVData(this.equipmentId);
			this.getEquipmentOtherData(this.equipmentId);
			this.getEquipmentMPPTData(this.equipmentId);

		},

		// 获取设备概览数据
		getDeviceOverview(equipmentId) {
			getDeviceOverview(equipmentId)
				.then((response) => {
					this.stationData = {
						...this.stationData,
						...response.data
					};

					this.updateItems();
				})
				.catch((error) => {
					console.error("获取设备概览数据失败:", error);
				});
		},

		// 获取设备PV数据 - 使用工具函数
		getEquipmentPVData(equipmentId) {
			if (!equipmentId) {
				console.warn('equipmentId is missing');
				return;
			}

			getEquipmentPVData(equipmentId)
				.then((response) => {
					if (response && response.data) {
						this.pvTableData.data = processPVData(response.data);
					} else {
						this.pvTableData.data = [];
						console.warn("获取到的PV数据为空");
					}
				})
				.catch((error) => {
					this.pvTableData.data = [];
					console.error("获取设备PV数据失败:", error);
				});
		},

		//获取设备基本数据
		getEquipmentBasicData(equipmentId) {
			getEquipmentBasicData(equipmentId)
				.then((response) => {
					this.equipmentBasicData = {
						...this.equipmentBasicData,
						...response.data
					};


					this.updateItems();
				})
				.catch((error) => {
					console.error("获取设备基本数据失败:", error);
				});
		},

		// 获取当前设备的交流信息 - 使用工具函数
		getEquipmentOtherData(equipmentId) {
			if (!equipmentId) {
				console.warn('equipmentId is missing');
				return;
			}

			getEquipmentOtherData(equipmentId)
				.then((response) => {
					if (response && response.data) {
						this.dataeletric = response.data;
						this.evTableData.data = processACData(response.data);
					}
				});
		},

		// 获取当前设备的MPPT数据 - 使用工具函数
		getEquipmentMPPTData(equipmentId) {
			if (!equipmentId) {
				console.warn('equipmentId is missing');
				return;
			}
			getEquipmentMPPTData(equipmentId)
				.then((response) => {
					if (response && response.data) {
						this.mpptTableData.data = processMPPTData(response.data);
					}
				})
				.catch((error) => {
					console.error("获取MPPT数据失败:", error);
					this.mpptTableData.data = [];
				});
		},


		// 获取当前设备发电趋势数据 - 使用工具函数
		getEquipmentCurveData(equipmentId, params = {}) {
			if (!equipmentId) {
				console.warn('equipmentId is missing');
				return;
			}

			let queryParams = {
				equipmentId: parseInt(equipmentId)
			};

			// 根据时间维度设置基础时间参数
			if (params.type === 'day') {
				queryParams = {
					...queryParams,
					year: parseInt(params.year),
					month: parseInt(params.month),
					day: parseInt(params.day)
				};
			} else if (params.type === 'month') {
				queryParams = {
					...queryParams,
					year: parseInt(params.year),
					month: parseInt(params.month)
				};
			} else if (params.type === 'year') {
				queryParams = {
					...queryParams,
					year: parseInt(params.year)
				};
			}

			// 使用工具函数获取数据类型参数
			queryParams.dataType = getChartDataTypeParams(this.AttributeColumnTabsCurrent);

			getEquipmentCurveData(equipmentId, queryParams)
				.then((response) => {
					if (response && response.data) {
						// 使用防抖函数处理数据更新
						this.debouncedUpdateChartDataFromResponse(response.data);
					}
				})
				.catch((error) => {
					console.error("获取设备发电趋势数据失败:", {
						message: error.message,
						response: error.response?.data,
						status: error.response?.status,
						params: queryParams
					});
				});
		},

		// 使用工具函数
		getChartTitle() {
			return getChartTitle(this.AttributeColumnTabsCurrent);
		},

		// 使用工具函数
		getLeftAxisLabel() {
			return getLeftAxisLabel(this.AttributeColumnTabsCurrent, this.currentDataType);
		},

		// 使用工具函数
		getRightAxisLabel() {
			return getRightAxisLabel();
		},

		// 使用工具函数
		getLegendItems() {
			return getChartLegendItems(this.AttributeColumnTabsCurrent, this.currentDataType, this.getVisibleLegendItems());
		},

		onChartTypeChanged(type) {
			this.chartType = type;
		},

		updateChartData() {
			switch (this.AttributeColumnTabsCurrent) {
				case 0: // 功率
					// 如果powerData是空的，创建一个空的图表数据
					if (!this.powerData.categories || this.powerData.categories.length === 0 ||
						!this.powerData.series[0].data || this.powerData.series[0].data.length === 0) {
						this.chartData = {
							categories: [],
							series: [
								{
									name: "总直流功率",
									data: []
								},
								{
									name: "总有功功率",
									data: []
								}
							]
						};
					} else {
						this.chartData = this.powerData;
					}
					this.showRightAxis = false;
					break;
				case 1: // 直流
					if (this.currentDataType === 'current') {
						if (!this.dcData.categories || this.dcData.categories.length === 0 ||
							!this.dcData.series || this.dcData.series.length === 0) {
							this.chartData = {
								categories: [],
								series: []
							};
						} else {
							this.chartData = {
								categories: this.dcData.categories,
								series: this.dcData.series
							};
						}
					} else {
						if (!this.dcData.categories || this.dcData.categories.length === 0 ||
							!this.dcData.voltage || this.dcData.voltage.length === 0) {
							this.chartData = {
								categories: [],
								series: []
							};
						} else {
							this.chartData = {
								categories: this.dcData.categories,
								series: this.dcData.voltage
							};
						}
					}
					this.showRightAxis = false;
					break;
				case 2: // 交流
					if (this.currentDataType === 'current') {
						if (!this.acData.categories || this.acData.categories.length === 0 ||
							!this.acData.series || this.acData.series.length === 0) {
							this.chartData = {
								categories: [],
								series: []
							};
						} else {
							this.chartData = {
								categories: this.acData.categories,
								series: this.acData.series
							};
						}
					} else {
						if (!this.acData.categories || this.acData.categories.length === 0 ||
							!this.acData.voltage || this.acData.voltage.length === 0) {
							this.chartData = {
								categories: [],
								series: []
							};
						} else {
							this.chartData = {
								categories: this.acData.categories,
								series: this.acData.voltage
							};
						}
					}
					this.showRightAxis = false;
					break;
			}
		},

		toggleShowMore() {
			this.showAllMppt = !this.showAllMppt;
		},

		toggleShowMorePv() {
			this.showAllPv = !this.showAllPv;
		},

		// 使用工具函数替换
		formatDate(date) {
			return formatDate(date);
		},

		// 使用工具函数替换
		formatYearMonth(date) {
			return formatYearMonth(date);
		},

		changeDate(direction) {
			const currentDateObj = new Date(this.currentDate);

			switch (this.dateCurrent) {
				case 0: // 日
					currentDateObj.setDate(currentDateObj.getDate() + direction);
					this.currentDate = formatDate(currentDateObj);
					this.getEquipmentCurveData(this.equipmentId, {
						year: currentDateObj.getFullYear(),
						month: currentDateObj.getMonth() + 1,
						day: currentDateObj.getDate(),
						type: 'day'
					});
					break;

				case 1: // 月
					currentDateObj.setMonth(currentDateObj.getMonth() + direction);
					this.currentDate = formatYearMonth(currentDateObj);
					this.getEquipmentCurveData(this.equipmentId, {
						year: currentDateObj.getFullYear(),
						month: currentDateObj.getMonth() + 1,
						type: 'month'
					});
					break;

				case 2: // 年
					currentDateObj.setFullYear(currentDateObj.getFullYear() + direction);
					this.currentDate = String(currentDateObj.getFullYear());
					this.getEquipmentCurveData(this.equipmentId, {
						year: currentDateObj.getFullYear(),
						type: 'year'
					});
					break;

				case 3: // 总计视图不需要切换
					return;
			}
		},

		// 使用工具函数替换
		formatTimeArray(timeArray) {
			return formatTimeArray(timeArray, this.dateCurrent);
		},

		// 使用工具函数处理图表数据
		updateChartDataFromResponse(data) {
			if (!data || !data.xaxis || !data.xaxis.length) {
				console.warn('No data or xaxis received');
				// 重置图表数据
				this.chartData = { categories: [], series: [] };
				return;
			}

			// 格式化时间轴
			const formattedTimes = formatTimeArray(data.xaxis, this.dateCurrent);

			// 使用工具函数处理图表数据
			const result = processChartData(data, this.AttributeColumnTabsCurrent, this.currentDataType, formattedTimes);

			// 更新对应的数据对象
			if (this.AttributeColumnTabsCurrent === 0 && result.powerData) {
				this.powerData = result.powerData;
				this.chartData = this.powerData;
			} else if (this.AttributeColumnTabsCurrent === 1 && result.dcData) {
				this.dcData = result.dcData;
				// 更新图表数据
				if (this.currentDataType === 'current') {
					this.chartData = {
						categories: this.dcData.categories,
						series: this.dcData.series
					};
				} else {
					this.chartData = {
						categories: this.dcData.categories,
						series: this.dcData.voltage
					};
				}
			} else if (this.AttributeColumnTabsCurrent === 2 && result.acData) {
				this.acData = result.acData;
				// 更新图表数据
				if (this.currentDataType === 'current') {
					this.chartData = {
						categories: this.acData.categories,
						series: this.acData.series
					};
				} else {
					this.chartData = {
						categories: this.acData.categories,
						series: this.acData.voltage
					};
				}
			}
		},

		// 数据类型切换处理 - 使用节流
		switchDataType(type) {
			// 使用节流函数处理切换
			this.throttledSwitchDataType(type);
		},

		// 实际处理数据类型切换的方法
		handleSwitchDataType(type) {
			if (this.currentDataType === type) return;

			// 更新数据类型
			this.currentDataType = type;

			// 直接更新图表，减少切换延迟感
			this.updateChartData();
		},

		// 使用工具函数
		getLegendTitle() {
			return getLegendTitle(this.AttributeColumnTabsCurrent, this.currentDataType);
		},

		getVisibleLegendItems() {
			if (this.AttributeColumnTabsCurrent === 1) {

				const pvItems = [];
				for (let i = 1; i <= 18; i++) { // 假设最多18个PV
					if (this.pvTableData.data.some(item => item.id === `PV${i}`)) {
						pvItems.push(`PV${i}`);
					}
				}
				return pvItems.length > 0 ? pvItems : ['PV1', 'PV2', 'PV3'];
			} else if (this.AttributeColumnTabsCurrent === 2) {
				return ['A相', 'B相', 'C相'];
			}
			return [];
		},

		// 使用工具函数获取颜色
		getLegendColor(index) {
			const legendItems = this.getLegendItems();
			return getChartColors(index, legendItems, this.acPhaseColors, this.legendColors, this.AttributeColumnTabsCurrent, this.getVisibleLegendItems());
		},
	}
};
</script>




<style lang="scss" scoped>
.container {
	padding: 0rpx;
	width: 100%;

	.top-tabs {
		background-color: #fff;
		padding: 0 90rpx;
	}

	// 运行趋势部分样式调整
	.run-trend {
		padding: 10rpx;

		.run-trend-part1 {
			.date-selector-container {
				display: flex;
				flex-direction: column;
				align-items: center;
				margin-bottom: 30rpx;
				padding: 20rpx 0;

				.data-subsection {
					width: 100%;
					margin-bottom: 20rpx;

					:deep(.u-subsection) {
						background-color: #f5f5f5;
						border-radius: 50rpx;
						overflow: hidden;
						box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
					}
				}

				.date-num {
					display: flex;
					justify-content: center;
					align-items: center;
					margin-top: 20rpx;
					background: linear-gradient(to right, #f8f8f8, #ffffff);
					padding: 15rpx 30rpx;
					border-radius: 50rpx;
					box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);

					.date-arrow {
						width: 70rpx;
						height: 70rpx;
						display: flex;
						justify-content: center;
						align-items: center;
						background-color: #ffffff;
						border-radius: 50%;
						box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
						transition: all 0.2s ease;

						&:active {
							background-color: #f0f0f0;
							transform: scale(0.95);
						}
					}

					.date-val {
						font-size: 32rpx;
						margin: 0 40rpx;
						color: #333;
						font-weight: 600;
						letter-spacing: 1rpx;
					}
				}
			}
		}

		.run-trend-part2 {
			.chart-tabs {
				margin-bottom: 30rpx;
				background: linear-gradient(to right, #f8f8f8, #ffffff);
				border-radius: 12rpx;
				padding: 5rpx;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);

				:deep(.u-tabs__wrapper__nav__item__text) {
					font-size: 28rpx;
					padding: 0 10rpx;
				}

				:deep(.u-tabs__wrapper__nav__line) {
					height: 6rpx;
					border-radius: 6rpx;
					bottom: 8rpx;
					transition: all 0.3s ease;
				}
			}

			.chart-container {
				margin: 30rpx 0;
				border-radius: 16rpx;
				overflow: hidden;
				background-color: #fff;
				position: relative;
				transition: all 0.3s ease;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
				padding: 20rpx;
				border: 1rpx solid #f0f0f0;
			}

			.data-type-selector {
				margin: 30rpx 0;
				background: linear-gradient(to right, #f8f8f8, #ffffff);
				border-radius: 12rpx;
				padding: 20rpx;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);

				.radio-group {
					display: flex;
					justify-content: center;
					gap: 80rpx;

					.radio-item {
						display: flex;
						align-items: center;
						padding: 15rpx 30rpx;
						border-radius: 50rpx;
						transition: all 0.3s ease;

						&.active {
							color: #40b780;
							background-color: rgba(64, 183, 128, 0.05);
							box-shadow: 0 2rpx 8rpx rgba(64, 183, 128, 0.1);
						}

						.radio-circle {
							width: 40rpx;
							height: 40rpx;
							border-radius: 50%;
							border: 2rpx solid #ddd;
							margin-right: 20rpx;
							display: flex;
							justify-content: center;
							align-items: center;
							transition: all 0.2s ease;

							.radio-inner {
								width: 26rpx;
								height: 26rpx;
								border-radius: 50%;
								background-color: #40b780;
								box-shadow: 0 0 5rpx rgba(64, 183, 128, 0.5);
							}
						}

						&.active .radio-circle {
							border-color: #40b780;
							transform: scale(1.05);
						}

						text {
							font-size: 28rpx;
							font-weight: 500;
						}
					}
				}
			}
		}

	/* 运行趋势折叠面板特殊样式 */
	.trend-collapse {
		:deep(.uni-collapse-item__title) {
			background: linear-gradient(to right, #f0f8f4, #ffffff);

			.uni-collapse-item__title-text {
				font-size: 34rpx;
				color: #333;
				font-weight: 600;
			}
		}
	}
	}


	.param-setting-box {
		padding: 20rpx;
	}

	.tele-sign-status {
		padding: 20rpx;

		.refresh-time {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20rpx;
			padding: 0 20rpx;

			.refresh-time-data {
				color: #a6a6a6;
				font-size: 24rpx;
			}
		}
	}



	// 自适应字体大小
	@media (max-width: 768px) {
		.tab {
			font-size: 24rpx;
		}
	}

	@media (max-width: 480px) {
		.tab {
			font-size: 28rpx;
			font-weight: bold;
			color: #b9b9b9;
		}
	}


	.setting-win {
		width: 80vw;
		display: flex;

		align-items: center;
		flex-direction: column;
		padding: 30rpx;

		.setting-top-part {
			// 顶部部分的样式
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;
			width: 100%;

			.win-title {
				// 标题的样式
				font-size: 32rpx;
				font-weight: bold;
				text-align: center;
				padding: 20rpx 0;
			}

			.ac-info-table {
				width: 100%;

				.table-header {
					display: flex;
					background-color: #f8f8f8;
					padding: 20rpx 0;

					.header-cell {
						flex: 1;
						text-align: center;
						font-weight: bold;
						font-size: 28rpx;
						color: #333;
					}
				}

				.table-row {
					display: flex;
					border-bottom: 1rpx solid #eee;

					.row-label {
						flex: 1;
						text-align: left;
						padding: 20rpx 0;
						font-size: 28rpx;
						color: #666;
					}

					.row-value {
						flex: 1;
						text-align: right;
						padding: 20rpx 0;
						font-size: 28rpx;
						color: #666;
					}
				}
			}
		}
	}

	/* 数据表格通用样式 */
	.data-table {
		width: 100%;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		background-color: #fff;

		.table-header {
			display: flex;
			background: linear-gradient(to right, #40b780, #3fc8ae);
			padding: 24rpx 0;

			.header-cell {
				flex: 1;
				text-align: center;
				font-weight: bold;
				font-size: 28rpx;
				color: #fff;
				text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
			}
		}

		.table-row {
			display: flex;
			border-bottom: 1rpx solid #f0f0f0;
			transition: background-color 0.3s;

			&:hover {
				background-color: #f9f9f9;
			}

			&.even-row {
				background-color: #f5f9f7;
			}

			.table-cell {
				flex: 1;
				text-align: center;
				padding: 24rpx 0;
				font-size: 28rpx;
				color: #666;
			}

			.pv-id, .phase-id, .mppt-id {
				font-weight: 600;
				color: #40b780;
			}
		}

		.no-data {
			text-align: center;
			padding: 40rpx;
			color: #999;
			font-size: 28rpx;
			background-color: #f9f9f9;
		}

		.show-more {
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 20rpx;
			color: #40b780;
			font-size: 28rpx;
			background-color: #f5f9f7;
			cursor: pointer;
			transition: all 0.3s;

			&:hover {
				background-color: #e8f5f0;
			}
		}
	}

	/* 特定表格样式 */
	.pv-table {
		margin-bottom: 10rpx;
	}

	.ac-table {
		margin-bottom: 10rpx;

		.phase-id {
			color: #3f51b5;
		}
	}

	.mppt-table {
		margin-bottom: 10rpx;

		.mppt-id {
			color: #ff9800;
		}
	}

	/* 自定义折叠面板样式 */
	.custom-collapse {
		:deep(.uni-collapse-item__title) {
			background: linear-gradient(to right, #f8f8f8, #ffffff);
			border-radius: 8rpx;
			margin-bottom: 10rpx;

			.uni-collapse-item__title-text {
				font-size: 32rpx;
				font-weight: 600;
				color: #333;
			}

			.uni-icons {
				color: #40b780;
			}
		}

		:deep(.uni-collapse-item__wrap) {
			background-color: transparent;
			border-radius: 8rpx;
			overflow: hidden;
		}
	}

	.chart-data-selector {
		display: flex;
		justify-content: center;
		padding: 20rpx 0;
		background-color: #f8f8f8;
		border-radius: 8rpx;
		margin: 20rpx 0;

		.selector-item {
			display: flex;
			align-items: center;
			margin: 0 30rpx;
			padding: 10rpx 20rpx;
			border-radius: 30rpx;

			&.active {
				color: #40b780;
			}

			text {
				margin-left: 10rpx;
				font-size: 26rpx;
			}
		}
	}

	.chart-legend-list {
		margin: 20rpx 0;
		padding: 20rpx;
		background-color: #fff;
		border-radius: 8rpx;

		.legend-title {
			font-size: 28rpx;
			font-weight: bold;
			margin-bottom: 20rpx;
			color: #333;
		}

		.legend-items {
			display: flex;
			flex-wrap: wrap;

			.legend-item {
				display: flex;
				align-items: center;
				margin-right: 30rpx;
				margin-bottom: 20rpx;

				.legend-color {
					width: 20rpx;
					height: 20rpx;
					border-radius: 4rpx;
					margin-right: 10rpx;
				}

				text {
					font-size: 24rpx;
					color: #666;
				}
			}
		}
	}
}
</style>