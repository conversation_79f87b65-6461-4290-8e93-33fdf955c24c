import request from '@/utils/request'


//获取单个逆变器数据
export function getDeviceOverview(equipmentId) {
    return request({
        url: `/app/equipmentBasicDetail`,
        method: 'get',
        params: { equipmentId },
    });
}

//提供电站发电趋势的数据，包含当日和历史
export function getStationCurveData({ stationId, year, month, day }) {
    // 构建查询参数
    const params = {};
    if (stationId !== undefined) params.stationId = stationId;
    if (year !== undefined) params.year = year;
    if (month !== undefined) params.month = month;
    if (day !== undefined) params.day = day;

    return request({
        url: '/app/stationCurveData',
        method: 'get',
        params, // 将查询参数传递给请求
    });
}




export function getStationwarning(alarmId) {
    return request({
        url: '/app/equipmentWarningDetail',
        method: 'get',
        params: { alarmId },

    })
}

export function getEquipmentBasicData(equipmentId) {
    return request({
        url: '/app/equipmentBasicData',
        method: 'get',
        params: { equipmentId },
    })
}

//获取单个设备PV数据
export function getEquipmentPVData(equipmentId) {
    return request({
        url: `/app/equipmentPVData`,
        method: 'get',
        params: { equipmentId }
    })
}

//获取设备中的交流、其他、设备信息的汇总接口
export function getEquipmentOtherData(equipmentId) {
    return request({
        url: `/app/equipmentOtherData`,
        method: 'get',
        params: { equipmentId }

    })
}

//获取设备MPPT数据
export function getEquipmentMPPTData(equipmentId) {
    return request({
        url: `/app/equipmentMPPTData`,
        method: 'get',
        params: { equipmentId }
    })
}

