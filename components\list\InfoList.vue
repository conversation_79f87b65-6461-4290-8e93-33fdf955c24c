<template>
  <view class="info-list">
    <uni-list>
      <view v-for="(item, index) in processedItems" :key="index">
        <uni-list-item
          :title="item.title"
          :rightText="item.rightText"
          :disabled="!isEditing"
          class="custom-item"
          @click="handleItemClick(item)"
        />
        <view v-if="item.value" class="value-text">{{ item.value }}</view>
      </view>
    </uni-list>
  </view>
</template>

<script>
export default {
  name: "InfoList",
  props: {
    items: {
      type: Array,
      required: true,
    },
    isEditing: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    processedItems() {
      return this.items.map((item) => ({
        ...item,
        rightText: String(item.rightText || ""), // 确保 rightText 始终是字符串
      }));
    },
  },
  methods: {
    handleItemClick(item) {
      ("设备名称点击", item); // 输出 item 内容来检查事件是否触发
      if (item.onClick && typeof item.onClick === "function") {
        item.onClick(); // 调用传入的 onClick 函数
      }
    },
  },
};
</script>
