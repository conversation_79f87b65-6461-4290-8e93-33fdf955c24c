<template>
  <view class="app-container">
    <view style="width: 100%; height: 60rpx; background-color: #ffffff"></view>
    <view class="top-box" style="background-color: #ffffff">
      <!-- 顶部筛选 -->
      <view class="address-select">
        <uv-drop-down
          ref="uvDropDown"
          sign="dropDown_1"
          text-active-color="#3c9cff"
          :extra-icon="{
            name: 'arrow-down-fill',
            color: '#666',
            size: '26rpx',
          }"
          :extra-active-icon="{
            name: 'arrow-up-fill',
            color: '#3c9cff',
            size: '26rpx',
          }"
          :defaultValue="defaultValue"
          :custom-style="{ padding: '0 30rpx' }"
          @click="selectMenu"
          :show="show"
          @close="closeDropDown"
        >
          <uv-drop-down-item
            name="addrList"
            type="2"
            :label="addrList.label"
            :value="addrList.value"
          ></uv-drop-down-item>
        </uv-drop-down>
        <uv-drop-down-popup
          sign="dropDown_1"
          :click-overlay-on-close="true"
          :currentDropItem="currentDropItem"
          @clickItem="clickItem"
          @popupChange="change"
        ></uv-drop-down-popup>
      </view>
      <view class="search">
        <u-search
          placeholder="电站名称或电站编号"
          v-model="searchVal"
          :showAction="false"
        ></u-search>
      </view>
    </view>

    <!-- Tab 切换 -->
    <view class="tab-box">
      <view
        class="tab-item"
        v-for="tab in tabs"
        :key="tab.id"
        :class="{ active: currentTab === tab.id }"
        @click="changeTab(tab.id)"
      >
        {{ tab.label }} ({{ tab.count }})
      </view>
    </view>

    <!-- 内容展示 -->
    <view class="card-list">
      <CardBox
        class="show-card"
        v-for="item in displayStationList"
        :key="item.stationId"
      >
        <view @click="toDataInfo(item)">
          <!-- 传递 item -->
          <view class="fir-part">
            <image
              src="../../static/images/banner/banner03.jpg"
              mode="aspectFill"
              class="station-img"
            >
            </image>
            <view class="station-info">
              <view class="station-title">
                <text>{{ item.fullName || item.status }}</text>
              </view>
              <view class="station-status">
                <view
                  class="status-tag"
                  :class="[getStatusClassComputed(item.status)]"
                >
                  <uni-icons
                    type="checkbox-filled"
                    size="14"
                    :style="{ color: getStatusColor(item.status) }"
                  ></uni-icons>
                  <text class="status-text">{{
                    getStatusText(item.status)
                  }}</text>
                </view>
              </view>
            </view>
          </view>
          <view class="sec-part">
            <view class="data-box">
              <view class="data-name"> 今日发电（度） </view>
              <view class="data-num">
                {{ item.dcap }}
              </view>
            </view>
            <view class="line-1"></view>
            <view class="data-box">
              <view class="data-name"> 实时功率（kW） </view>
              <view class="data-num">
                {{ item.acPower / 1000 }}
              </view>
            </view>
            <view class="line-1"></view>
            <view class="data-box">
              <view class="data-name">
                装机容量 {{ item.capacity > 1000000 ? "（MWp）" : "（kWp）" }}
              </view>
              <view class="data-num">
                {{
                  item.capacity > 1000000
                    ? (item.capacity / 1000000).toFixed(2)
                    : (item.capacity / 1000).toFixed(2)
                }}
              </view>
            </view>
            <view class="line-1"></view>
          </view>
          <view class="thi-part">
            <view class="line-2"></view>
            <view class="station-map">
              <uni-icons
                type="location-filled"
                size="16"
                style="color: #bbb"
              ></uni-icons>
              <text class="address">{{ item.place }}</text>
            </view>
          </view>
        </view>
      </CardBox>

      <!-- 加载更多提示 -->
      <view v-if="loading" class="loading-more">
        <u-loading-icon></u-loading-icon>
        <text>加载中...</text>
      </view>
      <view v-if="!hasMore" class="no-more">
        <text>没有更多数据了</text>
      </view>
    </view>
  </view>
</template>

<script>
import { getStationList } from "@/api/station/station.js";
import CardBox from "@/components/CardBox/CardBox.vue";

export default {
  components: {
    CardBox,
  },
  computed: {
    // 获取当前下拉筛选项
    currentDropItem() {
      return this[this.activeName];
    },
    // 根据当前选中的 Tab 过滤数据
    currentStationList() {
      const tab = this.tabs.find((t) => t.id === this.currentTab);
      console.log("当前tab数据:", tab); // 调试用
      return tab ? tab.data : [];
    },
    // 处理分页显示的数据
    displayStationList() {
      const tab = this.tabs.find((t) => t.id === this.currentTab);
      if (!tab) return [];
      return tab.data.slice(0, this.pageSize * this.currentPage);
    },
  },
  data() {
    return {
      defaultValue: [0, "0", "0"],
      result: [
        {
          name: "addrList",
          label: "全部状态",
          value: "all",
        },
      ],
      activeName: "addrList",
      addrList: {
        label: "电站状态",
        value: "all",
        activeIndex: 0,
        color: "#333",
        activeColor: "#2878ff",
        child: [
          {
            label: "全部",
            value: "all",
          },
          {
            label: "正常",
            value: "normal",
          },
          {
            label: "异常",
            value: "abnormal",
          },
          {
            label: "离线",
            value: "offline",
          },
          {
            label: "未接入",
            value: "notConnected",
          },
        ],
      },
      searchVal: "",
      active: 2,
      stationList: [], // 站点列表数据
      tabs: [
        {
          id: "normal",
          label: "正常",
        },
        {
          id: "new",
          label: "新建",
        },
        {
          id: "pending",
          label: "待处理",
        },
      ],
      currentTab: "all", // 当前选中的 Tab
      originalStationList: [], // 用于存储原始数据
      show: false, // 添加控制下拉框显示的状态
      pageSize: 5, // 每页显示数量
      currentPage: 1, // 当前页码
      loading: false, // 加载状态
      hasMore: true, // 是否还有更多数据
    };
  },
  onLoad() {
    const status = uni.getStorageSync("tabStatus"); // 读取状态
    console.log("状态值", status);
    this.changeTab(status);

    this.getStationList(); // 获取站点数据

    // 添加滚动监听
    uni.onWindowResize(() => {
      this.checkLoadMore();
    });
  },
  onReachBottom() {
    // 触底加载更多
    this.loadMore();
  },
  watch: {
    // 监听搜索值变化
    searchVal: {
      handler(newVal) {
        this.handleSearch(newVal);
      },
      immediate: true,
    },
  },
  methods: {
    async getStationList() {
      try {
        const res = await getStationList();
        if (res && res.rows) {
          this.originalStationList = res.rows;
          this.stationList = [...this.originalStationList];
          this.classifyTabs();
          console.log("获取到的电站列表:", this.stationList.status); // 调试用
        }
      } catch (error) {
        console.error("获取站点数据失败:", error);
      }
    },
    classifyTabs() {
      const statusSet = new Set([
        "all", // 全部
        "normal", // 正常
        "abnormal", // 异常
        "offline", // 离线
        "notConnected", // 未接入
      ]);

      const statusCount = {};
      const totalCount = this.stationList.length;

      // 初始化计数器
      statusSet.forEach((status) => {
        statusCount[status] = 0;
      });

      // 统计各状态数量
      this.stationList.forEach((item) => {
        // 根据实际返回的状态值进行映射
        let mappedStatus;
        switch (item.status) {
          case 1:
          case "1":
          case "normal":
          case "正常":
            mappedStatus = "normal";
            break;
          case 2:
          case "2":
          case "abnormal":
          case "异常":
            mappedStatus = "abnormal";
            break;
          case 3:
          case "3":
          case "offline":
          case "离线":
            mappedStatus = "offline";
            break;
          case 0: // 添加未接入的状态值
          case "0":
          case null:
          case undefined:
          case "":
          case "notConnected":
          case "未接入":
            mappedStatus = "notConnected";
            break;
          default:
            mappedStatus = "notConnected"; // 默认归类为未接入
        }

        statusCount[mappedStatus]++;
      });

      // 更新 tabs
      this.tabs = [
        {
          id: "all",
          label: "全部",
          count: totalCount,
          data: this.stationList,
        },
        {
          id: "normal",
          label: "正常",
          count: statusCount.normal || 0,
          data: this.stationList.filter(
            (item) =>
              item.status === 1 ||
              item.status === "1" ||
              item.status === "normal" ||
              item.status === "正常"
          ),
        },
        {
          id: "abnormal",
          label: "异常",
          count: statusCount.abnormal || 0,
          data: this.stationList.filter(
            (item) =>
              item.status === 2 ||
              item.status === "2" ||
              item.status === "abnormal" ||
              item.status === "异常"
          ),
        },
        {
          id: "offline",
          label: "离线",
          count: statusCount.offline || 0,
          data: this.stationList.filter(
            (item) =>
              item.status === 3 ||
              item.status === "3" ||
              item.status === "offline" ||
              item.status === "离线"
          ),
        },
        {
          id: "notConnected",
          label: "未接入",
          count: statusCount.notConnected || 0,
          data: this.stationList.filter(
            (item) =>
              item.status === 0 ||
              item.status === "0" ||
              item.status === null ||
              item.status === undefined ||
              item.status === "" ||
              item.status === "notConnected" ||
              item.status === "未接入" ||
              !item.status // 添加对空值的判断
          ),
        },
      ];

      if (
        !this.currentTab ||
        !this.tabs.find((tab) => tab.id === this.currentTab)
      ) {
        this.currentTab = "all";
      }
    },
    change(e) {
      // 可以在这里处理状态改变
      "dropdown state changed:", e;
    },
    selectMenu(e) {
      const { name, active, type } = e;
      this.activeName = name;
      const find = this.result.find((item) => item.name == this.activeName);
      if (find) {
        const findIndex = this[this.activeName].child.findIndex(
          (item) => item.label == find.label && item.value == find.value
        );
        this[this.activeName].activeIndex = findIndex;
      } else {
        this[this.activeName].activeIndex = 0;
      }
    },
    clickItem(e) {
      const { value } = e;
      // 根据选中的状态切换到对应的 Tab
      this.changeTab(value);
      // 直接修改显示状态来关闭下拉框
      this.show = false;
    },
    toDataInfo(item) {
      const id = item.stationId; // 获取点击项的 stationId

      uni.navigateTo({
        url: `./station?stationId=${id}`, // 使用反引号
      });
    },
    changeTab(tabId) {
      // Map 'connecting' to 'notConnected' if it comes directly from other pages
      if (tabId === "connecting") {
        tabId = "notConnected";
      }

      this.currentTab = tabId;
      uni.setStorageSync("tabStatus", tabId);
      this.currentPage = 1; // 重置页码
      this.hasMore = true; // 重置加载状态
      this.getStationList();
    },
    // 添加搜索处理方法
    handleSearch(keyword) {
      if (!keyword) {
        // 如果搜索框为空，恢复原始数据
        this.stationList = [...this.originalStationList];
      } else {
        // 根据关键词过滤数据
        const filteredList = this.originalStationList.filter(
          (item) =>
            item.fullName.toLowerCase().includes(keyword.toLowerCase()) ||
            item.stationId.toString().includes(keyword)
        );
        this.stationList = filteredList;
      }
      // 重新分类更新 tabs
      this.classifyTabs();

      this.currentPage = 1; // 重置页码
      this.hasMore = true; // 重置加载状态
      this.checkLoadMore();
    },
    // 修改获取状态文本的方法
    getStatusText(status) {
      const statusMap = {
        0: "未接入",
        0: "未接入",
        1: "正常",
        1: "正常",
        normal: "正常",
        正常: "正常",
        2: "异常",
        2: "异常",
        abnormal: "异常",
        异常: "异常",
        3: "离线",
        3: "离线",
        offline: "离线",
        离线: "离线",
        notConnected: "未接入",
        未接入: "未接入",
      };
      return statusMap[status] || "未接入"; // 默认返回未接入
    },
    // 修改状态样式类的方法
    getStatusClass(status) {
      const mappedStatus = this.getStatusText(status);
      const classMap = {
        正常: "status-normal",
        异常: "status-abnormal",
        离线: "status-offline",
        未接入: "status-notConnected",
      };
      return classMap[mappedStatus] || "status-normal";
    },
    // 为了兼容 uni-app 的 :class 语法，添加这个方法
    getStatusClassComputed(status) {
      return this.getStatusClass(status);
    },
    // 修改状态颜色的方法
    getStatusColor(status) {
      const mappedStatus = this.getStatusText(status);
      const colorMap = {
        正常: "#4fbb9d",
        异常: "#ff4d4f",
        离线: "#999999",
        未接入: "#faad14",
      };
      return colorMap[mappedStatus] || "#4fbb9d";
    },
    // 添加处理下拉框显示状态的方法
    toggleDropDown() {
      this.show = !this.show;
    },
    // 添加关闭下拉框的方法
    closeDropDown() {
      this.show = false;
    },
    // 检查是否需要加载更多
    checkLoadMore() {
      const tab = this.tabs.find((t) => t.id === this.currentTab);
      if (!tab) return;

      this.hasMore = this.pageSize * this.currentPage < tab.data.length;
    },

    // 加载更多数据
    async loadMore() {
      if (!this.hasMore || this.loading) return;

      this.loading = true;

      // 模拟异步加载
      await new Promise((resolve) => setTimeout(resolve, 800));

      this.currentPage++;
      this.checkLoadMore();
      this.loading = false;
    },
  },
};
</script>

<style lang="scss">
page {
  background-color: #f5f6f7;
}

.tab-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f5f5f5;
  background-color: #fff;
  padding: 0 20rpx;
  height: 80rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  &.active {
    color: #3c9cff;
    font-weight: 500;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 40rpx;
      height: 4rpx;
      background-color: #3c9cff;
      border-radius: 2rpx;
    }
  }
}

// 容器样式
.app-container {
  background-color: #f5f5f5;
}

// 顶部搜索样式
.top-box {
  display: flex;
  width: 100%;
  height: 70rpx;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding: 0 20rpx;

  .address-select {
    width: 30%;
    height: 100%;
    display: flex;
    align-items: center;
    // 这里可以设置地址部分的样式
  }

  .search {
    width: 65%;
    height: 100%;
    background-color: #fff;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    // 这里可以设置搜索部分的样式
  }

  .icons {
    display: flex;
    background-color: #fff;
    justify-content: space-around;
    align-items: center;
    width: 20%;
    height: 100%;
    // 这里可以设置图标部分的样式
  }
}

// 条件选择样式

// 内容展示样式
.card-list {
  // display: flex;
  // flex-wrap: wrap;
  padding: 20rpx;
  box-sizing: border-box;
  width: 100%;

  // 第一部分样式
  .fir-part {
    display: flex;

    .station-img {
      width: 100rpx;
      height: 100rpx;
      border-radius: 16rpx;
    }

    .station-info {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      margin-left: 20rpx;

      .station-title {
        font-size: 24rpx;
        font-weight: bold;
      }

      .station-status {
        margin-top: 4rpx;

        .status-tag {
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 8rpx;
          width: 110rpx;
          height: 40rpx;

          .status-text {
            font-size: 20rpx;
            white-space: nowrap;
            margin-bottom: 3rpx;
            margin-left: 4rpx;
          }
        }
      }
    }
  }

  // 第二部分样式
  .sec-part {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30rpx;

    .data-box {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      align-items: flex-start;
      height: 80rpx;

      .data-name {
        font-size: 22rpx;
        color: #aaa;
      }

      .data-num {
        font-size: 28;
        font-weight: bold;
        letter-spacing: 2rpx;
      }
    }

    .line-1 {
      width: 1rpx;
      height: 40rpx;
      background-color: #eee;
    }
  }

  // 第三部分样式
  .thi-part {
    margin-top: 18rpx;

    .line-2 {
      width: 100%;
      height: 1rpx;
      background-color: #eee;
      margin-bottom: 14rpx;
    }

    .station-map {
      width: 100%;
      display: flex;
      align-items: center;

      .address {
        margin-left: 8rpx;
        color: #aaa;
        font-size: 20rpx;
        letter-spacing: 1rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.station-status {
  margin-top: 4rpx;

  .status-tag {
    display: flex;
    align-items: center;
    padding: 4rpx 12rpx;
    border-radius: 4rpx;
    width: fit-content;
    height: 32rpx;

    .status-text {
      font-size: 22rpx;
      margin-left: 4rpx;
    }
  }

  // 正常状态
  .status-normal {
    background-color: #e6f6f1;
    .status-text {
      color: #4fbb9d;
    }
  }

  // 离线状态
  .status-offline {
    background-color: #f5f5f5;
    .status-text {
      color: #999999;
    }
  }

  // 故障状态
  .status-fault {
    background-color: #fff1f0;
    .status-text {
      color: #ff4d4f;
    }
  }

  // 告警状态
  .status-warning {
    background-color: #fff7e6;
    .status-text {
      color: #faad14;
    }
  }

  // 关机状态
  .status-shutdown {
    background-color: #f5f5f5;
    .status-text {
      color: #999999;
    }
  }
}

// 添加状态样式
.status-tag {
  &.status-normal {
    background-color: #e6f6f1;
    .status-text {
      color: #4fbb9d;
    }
  }
  &.status-abnormal {
    background-color: #fff1f0;
    .status-text {
      color: #ff4d4f;
    }
  }
  &.status-offline {
    background-color: #f5f5f5;
    .status-text {
      color: #999999;
    }
  }
  &.status-notConnected {
    background-color: #fff7e6;
    .status-text {
      color: #faad14;
    }
  }
}

.loading-more,
.no-more {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 24rpx;

  .u-loading-icon {
    margin-right: 10rpx;
  }
}
</style>