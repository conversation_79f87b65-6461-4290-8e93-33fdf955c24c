<template>
  <view class="table-container">
    <view v-for="(item, index) in items" :key="index">
      <view class="table-title">{{ item.title }}</view>
      <view v-for="(subItem, subIndex) in item.subItems" :key="subIndex">
        <view v-if="subItem.subtitle" class="subtitle">{{ subItem.subtitle }}</view>
        <view class="table-content">
          <!-- 表头 -->
          <view class="table-row table-header">
            <view class="cell">相位</view>
            <view class="cell">电压(V)</view>
            <view class="cell">电流(A)</view>
          </view>
          <!-- 数据行 -->
          <view 
            v-for="(row, rowIndex) in subItem.tableData" 
            :key="rowIndex"
            class="table-row"
          >
            <view class="cell">{{ row.phase }}</view>
            <view class="cell">{{ row.voltage }}</view>
            <view class="cell">{{ row.current }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CollapseTable',
  props: {
    items: {
      type: Array,
      required: true,
      default: () => []
    }
  }
}
</script>

<style scoped lang="scss">
.uni-collapse {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.uni-collapse-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  ::v-deep .uni-collapse-item__wrap {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
}

.uni-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.section {
  margin: 8px 0;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  
  & + .section {
    margin-top: 12px;
  }
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}

.uni-table {
  width: 100%;
  
  ::v-deep .uni-th,
  ::v-deep .uni-td {
    padding: 8px;
    text-align: center;
  }
  
  ::v-deep .uni-th {
    background-color: #f0f0f0;
    font-weight: bold;
  }
}

.table-container {
  background-color: #fff;
}

.table-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}

.subtitle {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 8px;
}

.table-content {
  background-color: #fff;
  border: 1px solid #eee;
  border-radius: 4px;
}

.table-row {
  display: flex;
  align-items: center;
  background-color: #fff;
  
  &:not(:last-child) {
    border-bottom: 1px solid #eee;
  }
}

.table-header {
  font-weight: bold;
  color: #333;
  border-bottom: 1px solid #eee;
  background-color: #fff;
}

.cell {
  flex: 1;
  padding: 12rpx 16rpx;
  text-align: center;
  font-size: 28rpx;
  background-color: #fff;
  
  border-right: 1px solid #eee;
  
  &:last-child {
    border-right: none;
  }
}

.table-header .cell {
  border-right: 1px solid #eee;
  
  &:last-child {
    border-right: none;
  }
}
</style>
