<template>
	<view class="login-bg">
		<br /><br /><br /><br /><br /><br /><br />

<view class="title">
  <image src="../static/demo1.png" class="large-icon" />

</view>

		<view class="t-login">
			<form class="cl">
				<view class="t-a">
					<image src="https://zhoukaiwen.com/img/loginImg/user.png"></image>
					<input v-model="loginForm.username" class="input" type="text" placeholder="请输入账号" maxlength="30" />
				</view>
				<view class="t-a">
					<image src="https://zhoukaiwen.com/img/loginImg/pwd.png"></image>
					<input v-model="loginForm.password" type="password" class="input" placeholder="请输入密码" maxlength="20" />
				</view>
				<view class="input-item flex align-center" style="width: 100%; margin: 0;" v-if="captchaEnabled">
				  <view class="iconfont icon-code icon"></view>
				  <input v-model="loginForm.code" type="number" class="inputcat" placeholder="请输入验证码" maxlength="4" />
				  <view class="login-code">
				    <image :src="codeUrl" @click="getCode" class="login-code-img"></image>
				  </view>
				</view>


				<button @click="handleLogin">登 录</button>
				<view class="t-c">
					<!-- <text class="t-c-txt" @tap="reg()">注册账号</text> -->
<!--					<text style="color: aquamarine;">返回首页</text>-->
				</view>
			</form>



		</view>
		<!-- <image class="img-a" src="https://zhoukaiwen.com/img/loginImg/bg1.png"></image> -->
	</view>
</template>

<script>
  import { getCodeImg } from '@/api/login'

  export default {
    data() {
      return {
        codeUrl: "",
        captchaEnabled: true,
        // 用户注册开关
        register: false,
        globalConfig: getApp().globalData.config,
        loginForm: {
          username: "admin",
          password: "admin123",
          code: "",
          uuid: ''
        }
      }
    },
    created() {
      this.getCode()
    },
    methods: {
      // 用户注册
      handleUserRegister() {
        this.$tab.redirectTo(`/pages/register`)
      },
      // 隐私协议
      handlePrivacy() {
        let site = this.globalConfig.appInfo.agreements[0]
        this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
      },
      // 用户协议
      handleUserAgrement() {
        let site = this.globalConfig.appInfo.agreements[1]
        this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
      },
      // 获取图形验证码
      getCode() {
        getCodeImg().then(res => {
          this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled
          if (this.captchaEnabled) {
            this.codeUrl = 'data:image/gif;base64,' + res.img
            this.loginForm.uuid = res.uuid
          }
        })
      },
      // 登录方法
      async handleLogin() {
        if (this.loginForm.username === "") {
          this.$modal.msgError("请输入您的账号")
        } else if (this.loginForm.password === "") {
          this.$modal.msgError("请输入您的密码")
        } else if (this.loginForm.code === "" && this.captchaEnabled) {
          this.$modal.msgError("请输入验证码")
        } else {
          this.$modal.loading("登录中，请耐心等待...")
          this.pwdLogin()
        }
      },
      // 密码登录
      async pwdLogin() {
        this.$store.dispatch('Login', this.loginForm).then(() => {
          this.$modal.closeLoading()
          this.loginSuccess()
        }).catch(() => {
          if (this.captchaEnabled) {
            this.getCode()
          }
        })
      },
      // 登录成功后，处理函数
      loginSuccess(result) {
        // 设置用户信息
        this.$store.dispatch('GetInfo').then(res => {
          this.$tab.reLaunch('/pages/index')
        })
      }
    }
  }
</script>

<style>
.t-a image {
    background-color: rgba(144, 238, 144, 0.5); /* 淡绿色背景 */
    border-radius: 50%; /* 可选：圆角效果 */
    padding: 5rpx; /* 可选：内边距 */
}

.input-item .icon {
    background-color: rgba(144, 238, 144, 0.1); /* 淡绿色背景 */
    border-radius: 50%; /* 可选：圆角效果 */
    padding: 5rpx; /* 可选：内边距 */
}

.title {
    text-align: center;
}

.large-icon {
    width: 400rpx; /* 调整为所需的大小 */
    height: 100rpx;
    margin-top: -400rpx; /* 与下方元素的距离 */
}

.img-a {
    width: 100%;
    position: absolute;
    bottom: 0;
}

.login-bg {
    height: 100vh; /* 可以调整为你需要的高度 */
    width: 100%; /* 使其占满全宽 */
    background-image: url('@/static/gree.png');
    background-size: cover; /* 使背景图覆盖整个容器 */
    background-position: center; /* 居中显示背景图 */
    padding-top: 0; /* padding-top 不应该是负值 */
}

.t-login {
    width: 700rpx;
    padding: 55rpx;
    margin: 80rpx auto;
    font-size: 28rpx;
    background-color: rgba(255, 255, 255, 0.5); /* 设置为半透明白色 */
    border-radius: 20rpx;
    box-shadow: 0 5px 7px 0 rgba(0, 0, 0, 0.15);
    z-index: 9;
}

.t-login button {
    font-size: 28rpx;
    background: linear-gradient(to right, #ff8f77, #fe519f); /* 继续使用你的渐变 */
    color: #fff;
    height: 90rpx;
    line-height: 90rpx;
    border-radius: 50rpx;
}

.t-login input {
    padding: 0 20rpx 0 100rpx; /* 调整左内边距 */
    height: 90rpx;
    line-height: 90rpx;
    margin-bottom: 50rpx;
    background: #f6f6f6; /* 保持原样 */
    border: 1px solid #ccc;
    font-size: 28rpx;
    border-radius: 50rpx;
}

.t-login .t-a {
    position: relative;
}

.input-item {
    display: flex;
    align-items: center;
    margin-top: 20rpx;
}

.login-code {
    height: 38rpx;
    float: right;
    margin-top: -100rpx;
}

.login-code-img {
    width: 160rpx; /* 调整验证码图片宽度 */
    height: 90rpx; /* 调整验证码图片高度 */
    border-radius: 30rpx; /* 使其变为椭圆角，半径为高度的一半 */
    cursor: pointer; /* 鼠标悬停时显示为可点击状态 */
    opacity: 0.7; /* 设置为半透明，值在0到1之间 */
    background-color: rgba(255, 255, 255, 0.2); /* 添加半透明背景色，适应不同场景 */
    margin-left: 50rpx;
}

.t-login .t-a image {
    width: 40rpx;
    height: 40rpx;
    position: absolute;
    left: 40rpx;
    top: 28rpx;
}

.t-login .t-b {
    text-align: left;
    font-size: 46rpx;
    color: #000;
    padding: 300rpx 0 120rpx 0;
    font-weight: bold;
}

.t-login .t-d {
    text-align: center;
    color: #999;
    margin: 80rpx 0;
}

.t-login .t-c {
    text-align: right;
    color: #666666;
    margin: 30rpx 30rpx 40rpx 0;
}

.t-login .t-c .t-c-txt {
    margin-right: 300rpx;
}

.t-login .t-e {
    text-align: center;
    width: 600rpx;
    margin: 40rpx auto 0;
}

.t-login .t-g {
    float: left;
    width: 33.33%;
}

.t-login .t-e image {
    width: 70rpx;
    height: 70rpx;
}

.t-login .t-f {
    text-align: center;
    margin: 80rpx 0 0 0;
    color: #999;
}

.t-login .t-f text {
    margin-left: 20rpx;
    color: #b9b9b9;
    font-size: 27rpx;
}

.t-login .uni-input-placeholder {
    color: #aeaeae;
}

.cl {
    zoom: 1;
}

.cl:after {
    clear: both;
    display: block;
    visibility: hidden;
    height: 0;
    content: '\20';
}

.inputcat {
    width: 60%;
}

/* 新增淡绿色背景 */
.t-login {
    background-color: rgba(144, 238, 144, 0.2); /* 整体淡绿色背景 */
}

.t-login input {
    background-color: rgba(255, 255, 255, 0.8); /* 输入框浅白色背景 */
}

.login-code-img {
    background-color: rgba(255, 255, 255, 0.8); /* 验证码图片背景 */
}


</style>
