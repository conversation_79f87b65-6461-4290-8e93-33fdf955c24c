<template>
  <view class="charts-box">
    <!-- 切换图表类型的按钮 -->
    <uni-icons
      type="tune"
      size="27"
      class="chart-toggle-btn"
      @click="toggleChartType"
    ></uni-icons>

    <!-- 动态绑定图表类型 -->
    <qiun-data-charts
      :key="chartType"
      :type="chartType"
      :opts="opts"
      :chart-data="chartData"
    />
    <!-- 动态显示单位 -->
    <view class="y-ahxis-label">
      {{ trendData.unitName }}({{ trendData.unit }})
    </view>
  </view>
</template>

<script>
export default {
  props: {
    categories: {
      type: Array,
      default: () => [],
    },
    series: {
      type: Array,
      default: () => [],
    },
    trendData: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      chartType: "column", // 默认是柱状图
      chartData: {},
      opts: {
        color: ["#40b780"],
        padding: [15, 15, 0, 15],
        dataLabel: false,
        dataPointShape: false,
        legend: {
          show: false,
        },
        xAxis: {
          // disableGrid: false,       // 启用 X 轴的网格线
          // gridType: "dash",         // 设置网格线为虚线
          // boundaryGap: false,       // 确保刻度线对齐图表边界
          tickCount: 5, // 增加刻度数量
          labelCount: 5, // 增加标签数量
          fontColor: "#666666", // 标签文字颜色
          backgroundColor: "transparent", // 设置背景透明

          labelCount: 6, // 增加标签的间隔，减少柱子密度
          formatter: function (val, index, opts) {
            if (index % 25 !== 0) return "";
            // 判断是否是数字且长度为3或4
            if (typeof val === "string" && /^\d{3,4}$/.test(val)) {
              // 处理时间格式
              const hour = val.length === 3 ? val[0] : val.slice(0, 2);
              const minute = val.length === 3 ? val.slice(1) : val.slice(2);
              return `${hour}:${minute}`;
            }
            return val;
          },
        },
        // 减小左侧或右侧的 padding，避免网格线过短
        yAxis: {
          gridType: "dash",
          dashLength: 10,
          data: [],
        },
        extra: {
          column: {
            type: "group",
            opacity: 0.8,
            width: 30,
            gradient: true,
            gradientColor: ["#40b780", "#a8f0d0"],
            linearType: "custom",
            barBorderRadius: [3, 3, 0, 0],
            shadowColor: "rgba(64, 183, 128, 0.2)",
            shadowBlur: 3,
            shadowOffsetX: 3,
            shadowOffsetY: 3,
            border: true,
            borderWidth: 1,
            borderColor: "#2a9c64",
          },
          tooltip: {
            showBox: true,
            showArrow: true,
            showCategory: true,
            boxPadding: 5,
            fontSize: 13,
            borderWidth: 0,
            borderRadius: 4,
            borderColor: "#000000",
            borderOpacity: 0.7,
            bgColor: "#000000",
            bgOpacity: 0.7,
            gridType: "solid",
            dashLength: 4,
            gridColor: "#CCCCCC",
            fontColor: "#FFFFFF",
            splitLine: true,
            horizentalLine: false,
            xAxisLabel: false,
            yAxisLabel: false,
            labelBgColor: "#FFFFFF",
            labelBgOpacity: 0.7,
            labelFontColor: "#666666",
          },
        },
      },
    };
  },

  watch: {
    categories: {
      handler(newVal) {
        this.updateChart();
      },
      deep: true,
    },
    series: {
      handler(newVal) {
        this.updateChart();
      },
      deep: true,
    },
  },

  methods: {
    updateChart() {
      if (!this.categories || !this.series) return;

      if (this.trendData.timeType === "day") {
        this.chartType = "area";
      } else {
        this.chartType = "column";
      }
      // 转换时间格式
      const formattedCategories = this.categories.map((val) => {
        // 如果值包含冒号（例如 "11:00"），直接返回原值
        if (typeof val === "string" && val.includes(":")) {
          return val;
        }

        // 判断是否为年视图（dateCurrent === 2），在年视图下，四位数字（如 "2024"）保留原值
        if (
          this.trendData.timeType === "total" &&
          typeof val === "string" &&
          /^\d{4}$/.test(val)
        ) {
          return val; // 在年视图下直接返回四位数字（年份）
        }

        // 判断是否是四位数时间（例如 "2020" 或 "0420"），且当前不是年视图时，转为时间格式
        if (
          this.trendData.timeType === "day" &&
          typeof val === "string" &&
          /^\d{4}$/.test(val)
        ) {
          const hour = parseInt(val.slice(0, 2));
          const minute = parseInt(val.slice(2));

          // 如果是有效时间（小时0-23，分钟0-59），转化为 "hh:mm" 格式
          if (hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59) {
            return `${val.slice(0, 2)}:${val.slice(2)}`;
          }
        }

        // 判断三位数时间（例如 "420"），将其补全为 "hh:mm" 格式
        if (
          this.trendData.timeType === "day" &&
          typeof val === "string" &&
          /^\d{3}$/.test(val)
        ) {
          const hour = "0" + val[0]; // 补充前导零
          const minute = val.slice(1);
          return `${hour}:${minute}`;
        }

        // 对其他类型的值不做转换，直接返回
        return val;
      });

      this.chartData = {
        categories: formattedCategories,
        series: this.series,
      };
    },

    // 用于切换图表类型的方法
    toggleChartType() {
      this.chartType = this.chartType === "column" ? "area" : "column";
    },
  },

  mounted() {
    this.updateChart();
  },
};
</script>
<style scoped>
/* 图表容器 */
.charts-box {
  width: 100%;
  height: 400rpx;
  position: relative;
}

/* 设置纵坐标单位的样式 */
.y-ahxis-label {
  position: absolute;
  left: 25rpx;
  top: -10rpx;
  transform: translateY(-60%);
  font-size: 14px;
  font-weight: bold;
  color: #333;
}

/* 图标按钮样式 */
.chart-toggle-btn {
  position: absolute;
  top: -42rpx; /* 调整垂直位置 */
  right: 10rpx; /* 右侧边距 */
  cursor: pointer;
}
</style>
