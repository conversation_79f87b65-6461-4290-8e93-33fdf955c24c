<template>
  <uni-collapse>
    <uni-collapse-item
      v-for="(item, index) in items"
      :key="index"
      :title="item.title"
      :open="true"
    >
      <uni-list>
        <uni-list-item
          v-for="(detail, idx) in item.details"
          :key="idx"
          :title="detail.title"
          :rightText="detail.rightText"
          showExtraIcon="true"
        />
      </uni-list>
    </uni-collapse-item>
  </uni-collapse>
</template>

<script>
export default {
  props: {
    items: {
      type: Array,
      required: true,

    },
    
  },
};
</script>
