<template>
  <view class="beautiful-progress-container" :style="containerStyle">
    <view class="beautiful-progress-track">
      <view
        class="beautiful-progress-bar"
        :class="progressBarClass"
        :style="{ width: `${percentage}%` }"
      ></view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'BeautifulProgress',
  props: {
    // 进度百分比，0-100
    percentage: {
      type: Number,
      default: 0,
      validator: (value) => value >= 0 && value <= 100
    },
    // 进度条颜色
    barColor: {
      type: String,
      default: '#4cd964'
    },
    // 背景颜色
    backgroundColor: {
      type: String,
      default: '#e9f5eb'
    },
    // 高度
    height: {
      type: [Number, String],
      default: 16
    },
    // 是否显示动画效果
    animated: {
      type: Boolean,
      default: true
    },
    // 圆角大小
    borderRadius: {
      type: [Number, String],
      default: 8
    },
    // 进度条样式类型
    type: {
      type: String,
      default: 'gradient', // 可选值: 'gradient', 'solid', 'striped'
      validator: (value) => ['gradient', 'solid', 'striped'].includes(value)
    }
  },
  computed: {
    // 容器样式
    containerStyle() {
      return {
        height: `${this.height}rpx`,
        borderRadius: `${this.borderRadius}rpx`
      };
    },
    // 进度条样式类
    progressBarClass() {
      return {
        'with-animation': this.animated,
        'gradient-bar': this.type === 'gradient',
        'solid-bar': this.type === 'solid',
        'striped-bar': this.type === 'striped'
      };
    }
  }
}
</script>

<style>
.beautiful-progress-container {
  width: 100%;
  overflow: hidden;
  position: relative;
  background-color: transparent;
}

.beautiful-progress-track {
  width: 100%;
  height: 100%;
  background-color: v-bind(backgroundColor);
  border-radius: inherit;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.beautiful-progress-bar {
  height: 100%;
  border-radius: inherit;
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  background: v-bind(barColor);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 渐变样式 */
.beautiful-progress-bar.gradient-bar {
  background: linear-gradient(90deg,
    v-bind(barColor) 0%,
    #2ab149 100%
  );
}

/* 条纹样式 */
.beautiful-progress-bar.striped-bar {
  background-image: linear-gradient(135deg,
    #4cd964 25%,
    #1e9c3d 25%,
    #1e9c3d 50%,
    #4cd964 50%,
    #4cd964 75%,
    #1e9c3d 75%
  );
  background-size: 16rpx 16rpx;
  opacity: 1;
}

.beautiful-progress-bar.with-animation::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: translateX(-100%);
  animation: shimmer 1.5s infinite;
  pointer-events: none;
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

/* 添加边框效果，替代脉动效果 */
.beautiful-progress-bar {
  border: 1px solid rgba(42, 177, 73, 0.5);
}
</style>
