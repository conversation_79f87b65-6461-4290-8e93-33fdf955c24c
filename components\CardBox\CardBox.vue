<template>
  <view @click="handleClick">
    <!-- 添加点击事件处理 -->
    <view class="card-box">
      <slot></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: "TitleCardBox",
  data() {
    return {};
  },
  methods: {
    handleClick() {
      this.$emit("click"); // 触发父组件的点击事件
    },
  },
};
</script>

<style lang="scss">
.card-box {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

  /* 添加文字样式 */
  color: #333333; /* 使用深色文字 */
  font-weight: 400; /* 正常字重 */

  /* 确保子元素中的文字也是清晰的 */
  ::v-deep {
    text {
      color: #333333;
      opacity: 1;
    }

    view {
      color: #333333;
      opacity: 1;
    }
  }
}

/* 如果你想保留阴影但让它更细微，可以使用这个设置 */
.card-box {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
</style>
